@component('mail::client.layout')
    {{-- Header --}}
    @slot('header')
        @component('mail::header', ['headerImage' => $headerImage])
        @endcomponent
    @endslot
    {{-- PreHeaderText - Custom data that comes from the database --}}
    @isset($preHeaderText)
        @slot('preHeaderText')
            <tr>
                <td>
                    {!! $preHeaderText !!}
                </td>
            </tr>
        @endslot
    @endisset
    {{-- HeaderText - Custom data that comes from the database --}}
    @isset($headerText)
        @slot('headerText')
            <tr>
                <td>
                    {!! $headerText !!}
                </td>
            </tr>
        @endslot
    @endisset
    {{-- IntroText - Custom data that comes from the database --}}
    @isset($introText)
        @slot('introText')
            <tr>
                <td>
                    {!! $introText !!}
                </td>
            </tr>
        @endslot
    @endisset
    {{-- Body --}}
    @isset($slot)
        <tr>
            <td>
                {{ $slot }}
            </td>
        </tr>
    @endisset
    {{-- FooterText - Custom data that comes from the database --}}
    @isset($footerText)
        @slot('footerText')
            <tr>
                <td>
                    {!! $footerText !!}
                </td>
            </tr>
        @endslot
    @endisset
    {{-- Unsubscribe - URL for unsubscribe emails --}}
    @isset($unsubscribe)
        @slot('unsubscribe')
            <tr>
                <td>
                    <p class="text-disclaimer">
                        @if(!empty($unsubscribe))
                            {!! $unsubscribe !!}
                        @else
                            @component('mail::unsubscribe')
                            @endcomponent
                        @endif
                    </p>
                </td>
            </tr>
        @endslot
    @endisset
    {{-- Footer --}}
    @slot('footer')
        @component('mail::client.footer')
            © {{ date('Y') }} {{ config('app.name') }}. @lang('All rights reserved.')
        @endcomponent
    @endslot
@endcomponent
