@component('mail::message', ['headerImage' => $headerImage])
    @slot('preHeaderImage', "investing2.png")
    @slot('headerText', $headerText)
    @slot('introText', $introText)
    @slot('footerText', $footerText)
    @slot('unsubscribe')
    @if (!empty($totalExpensesSummaryInfo))
        <div class="table-container">
            <table class="summary-info" align="center">
                @component('mail::bettertracker.summaryInfo')
                    @slot('summaryInfo', $totalExpensesSummaryInfo)
                @endcomponent
            </table>
        </div>
    @endif
    @if (!empty($largestExpenses) && count($largestExpenses) > 0)
        <p class="subtitle">LARGEST EXPENSES</p>
        <div class="table-container">
            <table class="summary-info" align="center">
                @foreach($largestExpenses as $largestExpense)
                    @component('mail::bettertracker.summaryInfo')
                        @slot('summaryInfo', $largestExpense)
                    @endcomponent
                @endforeach
            </table>
        </div>
    @endif
    @component('mail::button', ['url' => $buttonUrl, 'showLinkAsText' => true])
        Check Your Expenses
    @endcomponent
@endcomponent
