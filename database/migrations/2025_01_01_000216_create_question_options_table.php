<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("question_options", function (Blueprint $table) {
            $table->id();
            $table->bigInteger("question_id")->index();
            $table->string("key", 100);
            $table->string("display_value", 500);
            $table->boolean("is_archived");
            $table->timestamp("archived_at")->nullable();
            $table->bigInteger("option_order");
            $table->boolean("show_answer_option")->default(false);
            $table->timestamps();

            $table->unique(["question_id", "key"]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("question_options");
    }
};
