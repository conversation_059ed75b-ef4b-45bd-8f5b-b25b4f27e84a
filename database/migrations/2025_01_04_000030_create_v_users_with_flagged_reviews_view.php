<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("create view v_users_with_flagged_reviews(email, first_name, last_name, hubspot_contact_id) as
                            SELECT DISTINCT u.email, u.first_name, u.last_name, u.hubspot_contact_id
                            FROM reviews AS r
                                     JOIN users AS u ON r.reviewer_user_id = u.id
                            WHERE (r.status = 'flagged')
                              AND (u.status = 'active')
                            ORDER BY u.email;");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW v_users_with_flagged_reviews');
    }
};
