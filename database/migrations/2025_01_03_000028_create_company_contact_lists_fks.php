<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table("company_contact_lists", function (Blueprint $table) {
            $table->foreign("company_id")
                ->references("id")
                ->on("companies")
                ->cascadeOnDelete();
            $table->foreign("creator_id")
                ->references("id")
                ->on("users")
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table("company_contact_lists", function (Blueprint $table) {
            $table->dropForeign("company_contact_lists_company_id_foreign");
            $table->dropForeign("company_contact_lists_creator_id_foreign");
        });
    }
};
