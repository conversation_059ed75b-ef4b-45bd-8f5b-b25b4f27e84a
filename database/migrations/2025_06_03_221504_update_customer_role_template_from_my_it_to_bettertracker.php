<?php

use App\Models\Permission\Role\Role;
use App\Models\Permission\Role\TemplateRole;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{

    public array $mspCustomerRoles = [
        'title' => 'MSP Customer My IT Spend Admin',
        'display_name' => 'My IT Spend Admin',
        'description' => 'Full permissions to My IT Spend.',
    ];

    public array $mspCustomerRolesUpdate = [
        'title' => 'MSP Customer BetterTracker Admin',
        'display_name' => 'BetterTracker Admin',
        'description' => 'Full permissions to BetterTracker.',
    ];


    /**
     * Run the migrations.
     */
    public function up(): void
    {
        TemplateRole::where('title', $this->mspCustomerRoles['title'])
        ->where('display_name', $this->mspCustomerRoles['display_name'])
        ->where('description', $this->mspCustomerRoles['description'])
        ->update($this->mspCustomerRolesUpdate);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        TemplateRole::where('title', $this->mspCustomerRolesUpdate['title'])
        ->where('display_name', $this->mspCustomerRolesUpdate['display_name'])
        ->where('description', $this->mspCustomerRolesUpdate['description'])
        ->update($this->mspCustomerRoles);
    }
};
