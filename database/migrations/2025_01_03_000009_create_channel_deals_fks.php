<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table("channel_deals", function (Blueprint $table) {
            $table->foreign("category_id")
                ->references("id")
                ->on("categories")
                ->cascadeOnDelete();
            $table->foreign("company_id")
                ->references("id")
                ->on("companies")
                ->cascadeOnDelete();
            $table->foreign("created_by_user_id")
                ->references("id")
                ->on("users")
                ->cascadeOnDelete();
            $table->foreign("reviewer_user_id")
                ->references("id")
                ->on("users")
                ->cascadeOnDelete();
            $table->foreign("status_id")
                ->references("id")
                ->on("lookup_option_values")
                ->cascadeOnDelete();
            $table->foreign("sub_category_id")
                ->references("id")
                ->on("categories")
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table("channel_deals", function (Blueprint $table) {
            $table->dropForeign("channel_deals_category_id_foreign");
            $table->dropForeign("channel_deals_company_id_foreign");
            $table->dropForeign("channel_deals_created_by_user_id_foreign");
            $table->dropForeign("channel_deals_reviewer_user_id_foreign");
            $table->dropForeign("channel_deals_status_id_foreign");
            $table->dropForeign("channel_deals_sub_category_id_foreign");
        });
    }
};
