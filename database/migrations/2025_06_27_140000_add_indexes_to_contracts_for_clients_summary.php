<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to contracts table for better performance in clientsSummary endpoint
        
        // Index for owner_id to optimize JOIN with company_clients
        DB::statement('CREATE INDEX IF NOT EXISTS idx_contracts_owner_id ON contracts (owner_id)');
        
        // Index for parent_id to filter contracts without addons (WHERE parent_id IS NULL)
        DB::statement('CREATE INDEX IF NOT EXISTS idx_contracts_parent_id ON contracts (parent_id)');
        
        // Composite index for owner_id + parent_id for optimal performance in clientsSummary query
        DB::statement('CREATE INDEX IF NOT EXISTS idx_contracts_owner_parent ON contracts (owner_id, parent_id)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the indexes
        DB::statement('DROP INDEX IF EXISTS idx_contracts_owner_id');
        DB::statement('DROP INDEX IF EXISTS idx_contracts_parent_id');
        DB::statement('DROP INDEX IF EXISTS idx_contracts_owner_parent');
    }
};
