<?php

use App\Enums\Company\CompanyProfileTypes;
use App\Models\Company\CompanyType;
use App\Models\Profile\CompanyProfileType;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $profileType = CompanyProfileType::firstWhere(["value" => CompanyProfileTypes::VendorFree]);

        CompanyType::where("type_is_of_vendor", true)->update([
            "default_company_profile_type_id" => $profileType->id,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $profileType = CompanyProfileType::firstWhere(["value" => CompanyProfileTypes::VendorBasic]);

        CompanyType::where("type_is_of_vendor", true)->update([
            "default_company_profile_type_id" => $profileType->id,
        ]);
    }
};
