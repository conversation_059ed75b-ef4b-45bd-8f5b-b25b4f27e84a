<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table("company_folders", function (Blueprint $table) {
            $table->foreign("author_id")
                ->references("id")
                ->on("users")
                ->cascadeOnDelete();
            $table->foreign("subject_id")
                ->references("id")
                ->on("companies")
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table("company_folders", function (Blueprint $table) {
            $table->dropForeign("company_folders_author_id_foreign");
            $table->dropForeign("company_folders_subject_id_foreign");
        });
    }
};
