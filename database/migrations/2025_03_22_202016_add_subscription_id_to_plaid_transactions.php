<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plaid_transactions', function (Blueprint $table) {
            $table->unsignedBigInteger("plaid_subscription_id")->after('plaid_sub_category_id')->nullable();
            $table->foreign("plaid_subscription_id")->references("id")->on("plaid_subscriptions");

            $table->index([
                "plaid_subscription_id",
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plaid_transactions', function (Blueprint $table) {
            $table->dropColumn('plaid_subscription_id');
        });
    }
};
