<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Use raw SQL with IF NOT EXISTS to avoid duplicate index errors

        // Add indexes to company_clients table for better filter performance
        DB::statement('CREATE INDEX IF NOT EXISTS idx_company_clients_partnership_type_id ON company_clients (partnership_type_id)');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_company_clients_company_partnership ON company_clients (company_id, partnership_type_id)');

        // Add indexes to companies table for client filter queries
        DB::statement('CREATE INDEX IF NOT EXISTS idx_companies_industry ON companies (industry)');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_companies_address ON companies (address)');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_companies_industry_address ON companies (industry, address)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Use raw SQL with IF EXISTS to safely drop indexes
        DB::statement('DROP INDEX IF EXISTS idx_company_clients_partnership_type_id');
        DB::statement('DROP INDEX IF EXISTS idx_company_clients_company_partnership');
        DB::statement('DROP INDEX IF EXISTS idx_companies_industry');
        DB::statement('DROP INDEX IF EXISTS idx_companies_address');
        DB::statement('DROP INDEX IF EXISTS idx_companies_industry_address');
    }
};
