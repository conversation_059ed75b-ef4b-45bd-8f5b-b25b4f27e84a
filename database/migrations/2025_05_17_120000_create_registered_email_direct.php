<?php

use App\Enums\Email\EmailBladeFiles;
use App\Models\Email\Email;
use App\Services\Email\EmailService;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->registeredEmailDirect();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Email::whereIn('blade_file_name', [EmailBladeFiles::RegisteredEmailDirect])->delete();
    }

    private function registeredEmailDirect(): void
    {
        EmailService::createBaseEmail(EmailBladeFiles::RegisteredEmailDirect);
    }
};
