<?php

use App\Models\ContractBillingContact;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $billingContacts = [
            [
                'key' => 'phone',
                'label' => 'Phone',
                'type' => 'string',
            ],
            [
                'key' => 'email',
                'label' => 'Email',
                'type' => 'string',
            ],
            [
                'key' => 'fax',
                'label' => 'Fax',
                'type' => 'string',
            ],
            [
                'key' => 'alternative_phone',
                'label' => 'Alternative Phone',
                'type' => 'string',
            ],
        ];

        ContractBillingContact::insert($billingContacts);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        ContractBillingContact::query()->delete();
    }
};
