<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("poll_options", function (Blueprint $table) {
            $table->id();
            $table->bigInteger("poll_question_id")->index();
            $table->string("option", 200);
            $table->boolean("shows_on_reports")->default(false);
            $table->bigInteger("order")->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("poll_options");
    }
};
