<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plaid_notify_alerts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->boolean('active')->default(false);
            $table->foreignId('type_id')
                ->references('id')
                ->on('lookup_option_values')
                ->cascadeOnDelete();
            $table->foreignId('period_id')
                ->nullable()
                ->references('id')
                ->on('lookup_option_values')
                ->cascadeOnDelete();
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies');

            $table->index([
                "company_id",
                "active",
                "type_id",
                "period_id",
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plaid_notify_alerts');
    }
};
