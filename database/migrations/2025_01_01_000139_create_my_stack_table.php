<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("my_stack", function (Blueprint $table) {
            $table->id();
            $table->bigInteger("user_id")->nullable()->index();
            $table->bigInteger("company_id")->nullable()->index();
            $table->bigInteger("stack_company_id")->nullable();
            $table->bigInteger("category_id")->nullable()->index();
            $table->bigInteger("product_id")->nullable()->index();
            $table->string("partner_status", 255)->nullable();
            $table->bigInteger("parent_stack_id")->nullable();
            $table->boolean("is_recommended_stack")->default(false);
            $table->timestamps();
            $table->softDeletes();

            $table->unique(["company_id", "stack_company_id", "category_id", "product_id", "is_recommended_stack"]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("my_stack");
    }
};
