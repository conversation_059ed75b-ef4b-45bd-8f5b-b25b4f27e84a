<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_email_template_texts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_id')
                ->references('id')
                ->on('emails')
                ->cascadeOnDelete();
            $table->foreignId('company_id')
                ->references('id')
                ->on('companies')
                ->cascadeOnDelete();
            $table->string('subject')->nullable();
            $table->text('header_text')->nullable();
            $table->text('intro_text')->nullable();
            $table->text('footer_text')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_email_template_texts');
    }
};
