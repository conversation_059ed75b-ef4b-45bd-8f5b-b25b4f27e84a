<?php

use App\Helpers\PermissionsHelper;
use App\Models\Permission\Permission;
use App\Models\Permission\Role\Role;
use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;

return new class() extends Migration {
    // PERMISSIONS
    private array $sharedClaimerAdminReadOnly = [
        'GET:api/v1/partners/{company}/users',
    ];

    private array $companyAdmin = [
        'GET:api/v1/admin/company',
        'POST:api/v1/company/store',
        'PUT:api/v1/admin/company/{company}/partner_flag',
        'POST:api/v1/admin/company/{company}/sync-hubspot-id',
        'POST:api/v1/admin/company/sync-all-hubspot',
        'GET:api/v1/admin/profile-enrichment/questions',
        'POST:api/v1/admin/profile-enrichment/questions/store',
        'PUT:api/v1/admin/profile-enrichment/questions/update',
        'DELETE:api/v1/admin/profile-enrichment/questions/delete',
        'POST:api/v1/admin/company/{company}/subscription',
        'POST:api/v1/company/claim-profile',
        'GET:api/v1/admin/company/{company}/claimers/search-users',
        'DELETE:api/v1/admin/company/{company}/claimers/delete',
        'GET:api/v1/admin/company/{company}/details',
    ];
    private array $companyProfileAdmin = [
        'PUT:api/v1/company/update',
        'POST:api/v1/profiles/vendor-basic/{companyId}/video/store',
        'POST:api/v1/blog/store',
        'PUT:api/v1/blog/update',
        'DELETE:api/v1/blog/delete',
        'POST:api/v1/profiles/vendor-basic/{companyId}/document/store',
        'GET:api/v1/profile-enrichment/questions',
        'POST:api/v1/profile-enrichment/company/{friendly_url}/answers/store',
        'POST:api/v1/company/{company}/product/{product}/store',
        'PUT:api/v1/company/{company}/product/{product}/update',
        'POST:api/profiles/vendor-basic/{companyId}/overview/sync-countries',
        'POST:api/profiles/vendor-basic/{companyId}/overview/sync-languages',
        'PUT:api/v1/profiles/vendor-basic/{companyId}/contact-info/update',
        'POST:api/v1/media/update/{media}',
    ];
    private array $channelCommandAdmin = [
        'POST:api/v1/profiles/vendor-basic/{companyId}/document/store',
        'POST:api/v1/media/update/{media}',
        'POST:api/v1/partner/page/{partnerPage}/section/content/store',
        'PUT:api/v1/blog/update',
        'POST:api/v1/blog/store',
        'DELETE:api/v1/blog/delete',
        'POST:api/v1/profiles/vendor-basic/{companyId}/video/store',
        'PUT:api/v1/partner/page/{partnerPage}/section/content/hide',
        'POST:api/v1/partner/page/header-image/update',
    ];
    private array $userManagementAdmin = [
        'GET:api/v1/admin/user',
        'GET:api/v1/admin/user/login-history',
        'PUT:api/v1/admin/user/change-status',
    ];
    private array $industryCalendarAdmin = [
        'GET:api/v1/admin/industry-events',
        'POST:api/v1/admin/industry-events/store',
        'POST:api/v1/admin/industry-events/update',
        'PUT:api/v1/admin/industry-events/bulk/update-status',
        'DELETE:api/v1/admin/industry-events/bulk/delete',
    ];
    private array $reportsAdmin = [
        'GET:api/v1/admin/reports/pitch/mspusers/{pitch_event_id}',
        'GET:api/v1/admin/reports/pitch/vendorusers/{pitch_event_id}',
        'GET:api/v1/admin/pitch-events/{pitchEvent}/report/complete/{vendor}',
        'GET:api/v1/admin/analytics/events/attendeecount',
        'POST:api/v1/admin/user/count',
        'GET:api/v1/admin/review/products/export-to-csv',
        'GET:api/v1/admin/reports/product/review',
        'GET:api/v1/admin/customview',
        'POST:api/v1/admin/customview/createreport',
    ];
    private array $productReviewsAdmin = [
        'GET:api/v1/admin/review/products',
        'GET:api/v1/admin/review/{review}',
        'GET:api/v1/admin/review/incentives/{reviewer}',
        'PUT:api/v1/admin/review/under-review',
        'PUT:api/v1/admin/review/flag',
        'PUT:api/v1/admin/review/approve',
        'PUT:api/v1/admin/review/incentive-update',
        'POST:api/v1/review/{review}/answers/sync',
    ];
    private array $categoriesTagsManagement = [
        'GET:api/v1/admin/categories',
        'GET:api/v1/admin/tags/search',
        'PUT:api/v1/admin/categories/update',
        'POST:api/v1/admin/categories/store',
        'POST:api/v1/admin/tags/store',
        'PUT:api/v1/admin/tags/update',
    ];
    private array $advertisementManagement = [
        'GET:api/v1/admin/advertisement',
        'GET:api/v1/admin/advertisement/find-vendors',
        'POST:api/v1/admin/advertisement/store',
        'POST:api/v1/admin/advertisement/update',
        'DELETE:api/v1/admin/advertisement/delete',
    ];
    private array $engageManagement = [
        'POST:api/v1/pitch-events/store',
        'POST:api/v1/pitch-events/update',
        'POST:api/v1/pitch-events/schedule-vendor/store',
        'DELETE:api/v1/pitch-events/schedule-vendor/delete',
        'DELETE:api/v1/pitch-events/delete',
        'GET:api/v1/admin/pitch-events/past',
    ];
    private array $engageVideoUpload = [
        'POST:api/v1/admin/video/{vendor}/add-to-vendor',
    ];
    private array $engageChannelEngage = [
        'POST:api/v1/pitch-events/pitch-day/send-broadcast-message',
        'PUT:api/v1/pitch-events/pitch-day/update-broadcast-message',
        'POST:api/v1/pitch-events/pitch-day/send-poll',
        'POST:api/v1/admin/pitch-day/delete-public-chat-msg',
        'POST:api/v1/pitch-events/pitch-day/send-mod-message-to-user',
        'POST:api/v1/admin/pitch-day/ban-user-public-chat-msg',
    ];
    private array $engagePolls = [
        'POST:api/v1/polls/questions/store',
        'POST:api/v1/polls/questions/options/store',
        'PUT:api/v1/polls/questions/options/update',
        'DELETE:api/v1/polls/questions/delete',
        'POST:api/v1/polls/questions/archive',
    ];
    private array $flaggedComments = [
        'GET:api/v1/admin/comments',
        'PUT:api/v1/admin/comments/reject',
        'PUT:api/v1/admin/comments/approve',
    ];
    private array $inAppEmails = [
        'GET:api/v1/admin/email',
        'GET:api/v1/admin/email/{email}',
        'PUT:api/v1/admin/email/{email}/template-text/update',
    ];

    private Collection $customerSuccessManager;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // PERMISSIONS CHANGED https://channelprogram.atlassian.net/browse/CPK-1139
        /*
        $date = now();
        // Creating the new role
        $role = Role::create([
            'title' => 'Customer Success Manager',
            'key' => RoleKeys::CUSTOMERSUCCESSMANAGER,
            'description' => 'Customer Success Manager',
            'is_admin' => true,
            'created_at' => $date,
            'updated_at' => $date
        ]);
        $this->getRolePermissionsList();
        $this->initializePermissionsRoles(
            $this->customerSuccessManager->pluck('title'),
            $role
        );*/
    }

    private function getRolePermissionsList()
    {
        $this->customerSuccessManager = collect();

        $routes = Route::getRoutes()->getRoutes();
        foreach ($routes as $route) {
            $permission = PermissionsHelper::makeArrayPermissionFromRoute($route);
            // customerSuccessManager
            if (
                in_array($permission['title'], $this->sharedClaimerAdminReadOnly) ||
                in_array($permission['title'], $this->companyProfileAdmin) ||
                in_array($permission['title'], $this->channelCommandAdmin) ||
                in_array($permission['title'], $this->productReviewsAdmin) ||
                in_array($permission['title'], $this->flaggedComments) ||
                in_array($permission['title'], $this->userManagementAdmin) ||
                in_array($permission['title'], $this->companyAdmin) ||
                in_array($permission['title'], $this->categoriesTagsManagement) ||
                in_array($permission['title'], $this->advertisementManagement) ||
                in_array($permission['title'], $this->industryCalendarAdmin) ||
                in_array($permission['title'], $this->reportsAdmin) ||
                in_array($permission['title'], $this->engageManagement) ||
                in_array($permission['title'], $this->engageVideoUpload) ||
                in_array($permission['title'], $this->engageChannelEngage) ||
                in_array($permission['title'], $this->inAppEmails) ||
                in_array($permission['title'], $this->engagePolls)
            ) {
                if (!$this->customerSuccessManager->contains($permission)) {
                    $this->customerSuccessManager->push($permission);
                }
            }
        }
    }

    private function initializePermissionsRoles(Collection $permissionsTitles, Role $role): void
    {
        Log::debug('SEED_PERMISSIONS::TRYING TO SAVE ' . $role->key . ' PERMISSIONS_ROLES');
        if ($permissionsTitles->count() === 0) {
            Log::debug('SEED_PERMISSIONS::NO ' . $role->key . ' PERMISSIONS_ROLES FOUND');

            return;
        }

        $permissions_roles = collect();
        $date = Carbon::now();
        $permissions = Permission::whereIn('title', $permissionsTitles->toArray())
            ->get();
        Log::debug('SEED_PERMISSIONS::PERMISSIONS: ' . $permissions->pluck('id'));
        $permissions->each(function ($permission) use ($permissions_roles, $role, $date) {
            $permissions_roles->push([
                'permission_id' => $permission->id,
                'role_id' => $role->id,
                'created_at' => $date,
                'updated_at' => $date,
            ]);
        });
        Log::debug('SEED_PERMISSIONS::' . $role->key . '(' . $permissions_roles->count() . ')' . ' PERMISSIONS_ROLES SAVED');
        DB::table('permissions_roles')->insert($permissions_roles->toArray());
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        /*
        $managerRole = Role::firstWhere('key', RoleKeys::CUSTOMERSUCCESSMANAGER);
        if (!$managerRole) return;
        PermissionRole::where(['role_id' => $managerRole->id])->delete();
        $managerRole->delete();
        */
    }
};
