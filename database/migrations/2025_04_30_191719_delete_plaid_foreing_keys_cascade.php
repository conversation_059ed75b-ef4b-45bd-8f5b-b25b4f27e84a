<?php

use App\Helpers\DatabaseHelper;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plaid_notify_alerts', function (Blueprint $table) {
            DatabaseHelper::dropForeignKeyIfExists('plaid_notify_alerts', 'company_id', $table);
        });

        Schema::table('plaid_notify_recipients', function (Blueprint $table) {
            DatabaseHelper::dropForeignKeyIfExists('plaid_notify_recipients', 'company_id', $table);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plaid_notify_alerts', function (Blueprint $table) {
            $table->foreign('company_id')->references('id')->on('companies');
        });

        Schema::table('plaid_notify_recipients', function (Blueprint $table) {
            $table->foreign('company_id')->references('id')->on('companies');
        });
    }
};
