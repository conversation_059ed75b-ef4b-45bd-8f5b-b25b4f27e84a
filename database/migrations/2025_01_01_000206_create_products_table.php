<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("products", function (Blueprint $table) {
            $table->id();
            $table->bigInteger("company_id")->index();
            $table->string("name", 255);
            $table->text("description");
            $table->string("url", 2048)->nullable();
            $table->text("features")->nullable();
            $table->string("friendly_url", 255)->nullable();
            $table->timestamps();

            $table->index(["friendly_url", "company_id", "name", "description", "url", "created_at", "updated_at"],
                "idx_products_friendly_url");
            $table->index(["company_id", "name", "description", "url", "created_at", "updated_at", "friendly_url"],
                "idx_products_company_id");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("products");
    }
};
