<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("pitch_events_attendants_users", function (Blueprint $table) {
            $table->id();
            $table->bigInteger("pitch_event_id")->index();
            $table->bigInteger("user_id")->index();
            $table->boolean("entered")->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("pitch_events_attendants_users");
    }
};
