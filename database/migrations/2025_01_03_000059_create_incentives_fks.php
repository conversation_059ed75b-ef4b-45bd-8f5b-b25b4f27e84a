<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table("incentives", function (Blueprint $table) {
            $table->foreign("review_id")
                ->references("id")
                ->on("users")
                ->cascadeOnDelete();
            $table->foreign("reviewed_by")
                ->references("id")
                ->on("users")
                ->cascadeOnDelete();
            $table->foreign("reviewer_id")
                ->references("id")
                ->on("users")
                ->cascadeOnDelete();
            $table->foreign("tango_catalog_id")
                ->references("id")
                ->on("tango_catalogs")
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table("incentives", function (Blueprint $table) {
            $table->dropForeign("incentives_review_id_foreign");
            $table->dropForeign("incentives_reviewed_by_foreign");
            $table->dropForeign("incentives_reviewer_id_foreign");
            $table->dropForeign("incentives_tango_catalog_id_foreign");
        });
    }
};
