<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("company_claimers_responses", function (Blueprint $table) {
            $table->id();
            $table->string("status", 20);
            $table->string("response", 20);
            $table->string("referral", 255)->nullable();
            $table->bigInteger("user_id")->index();
            $table->bigInteger("company_id")->index();
            $table->timestamps();

            $table->unique(["user_id", "company_id"]);
            $table->index(["user_id", "company_id"]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("company_claimers_responses");
    }
};
