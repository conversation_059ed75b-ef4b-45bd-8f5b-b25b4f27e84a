<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("question_answers", function (Blueprint $table) {
            $table->id();
            $table->bigInteger("user_id")->index();
            $table->bigInteger("question_id")->nullable()->index();
            $table->bigInteger("question_option_id")->nullable()->index();
            $table->text("answer")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("question_answers");
    }
};
