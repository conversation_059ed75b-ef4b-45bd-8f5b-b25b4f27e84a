<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table("company_languages_supported", function (Blueprint $table) {
            $table->foreign("company_id")
                ->references("id")
                ->on("companies")
                ->cascadeOnDelete();
            $table->foreign("language_id")
                ->references("id")
                ->on("languages")
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table("company_languages_supported", function (Blueprint $table) {
            $table->dropForeign("company_languages_supported_company_id_foreign");
            $table->dropForeign("company_languages_supported_language_id_foreign");
        });
    }
};
