<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("company_contact_information", function (Blueprint $table) {
            $table->id();
            $table->bigInteger("company_id")->unique()->index();
            $table->string("manager_name", 255)->nullable();
            $table->string("manager_email", 100)->nullable();
            $table->string("manager_phone", 25)->nullable();
            $table->string("manager_handle", 50)->nullable();
            $table->string("sales_email", 100)->nullable();
            $table->string("sales_phone", 25)->nullable();
            $table->string("support_email", 100)->nullable();
            $table->string("support_phone", 25)->nullable();
            $table->string("toll_free_phone", 20)->nullable();
            $table->string("partners_phone", 20)->nullable();
            $table->string("partners_email", 100)->nullable();
            $table->string("media_email", 100)->nullable();
            $table->json("additional_websites")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("company_contact_information");
    }
};
