<?php

use App\Models\Currency;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    private array $currencies = [
        ["key" => "USD", "order" => 1, "name" => "United States Dollar", "symbol" => "$"],
        ["key" => "CAD", "order" => 2, "name" => "Canadian Dollar", "symbol" => "$"],
        ["key" => "AUD", "order" => 3, "name" => "Australian Dollar", "symbol" => "$"],
        ["key" => "EUR", "order" => 4, "name" => "Euro", "symbol" => "€"],
        ["key" => "GBP", "order" => 5, "name" => "Pound Sterling", "symbol" => "£"],
        ["key" => "NZD", "order" => 6, "name" => "New Zealand Dollar", "symbol" => "$"],
    ];
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        foreach ($this->currencies as $currency) {
            Currency::updateOrCreate([
                "key" => $currency["key"],
            ],[
                "order" => $currency["order"],
                "name" => $currency["name"],
                "symbol" => $currency["symbol"],
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Currency::whereNot("key", "USD")->delete();
    }
};
