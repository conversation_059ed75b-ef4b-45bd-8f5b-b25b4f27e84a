<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("media", function (Blueprint $table) {
            $table->id();
            $table->string("model_type", 255);
            $table->bigInteger("model_id");
            $table->uuid()->unique();
            $table->string("collection_name", 255);
            $table->string("name", 255);
            $table->string("file_name", 255);
            $table->string("mime_type", 255)->nullable();
            $table->string("disk", 255);
            $table->string("conversions_disk", 255)->nullable();
            $table->bigInteger("size");
            $table->json("manipulations");
            $table->json("custom_properties");
            $table->json("generated_conversions");
            $table->json("responsive_images");
            $table->bigInteger("order_column")->nullable();
            $table->timestamps();

            $table->index(["model_type", "model_id"]);
            $table->index(["collection_name", "model_id", "model_type", "uuid", "name", "file_name", "mime_type",
                "disk", "conversions_disk", "size", "manipulations", "custom_properties", "generated_conversions",
                "responsive_images", "order_column", "created_at", "updated_at"],
                "idx_media_collection_model_id");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("media");
    }
};
