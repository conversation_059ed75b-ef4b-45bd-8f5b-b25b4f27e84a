<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to company_clients table for better filter performance
        Schema::table('company_clients', function (Blueprint $table) {
            // Index for company_id (already exists as foreign key, but adding explicit index)
            $table->index('company_id', 'idx_company_clients_company_id');

            // Index for partnership_type_id for partnership filter queries
            $table->index('partnership_type_id', 'idx_company_clients_partnership_type_id');

            // Composite index for company_id + partnership_type_id for faster filtering
            $table->index(['company_id', 'partnership_type_id'], 'idx_company_clients_company_partnership');
        });

        // Add indexes to companies table for client filter queries
        Schema::table('companies', function (Blueprint $table) {
            // Index for industry column for industry filter
            $table->index('industry', 'idx_companies_industry');

            // Index for address column for location filter
            $table->index('address', 'idx_companies_address');

            // Composite index for commonly queried fields together
            $table->index(['industry', 'address'], 'idx_companies_industry_address');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_clients', function (Blueprint $table) {
            $table->dropIndex('idx_company_clients_company_id');
            $table->dropIndex('idx_company_clients_partnership_type_id');
            $table->dropIndex('idx_company_clients_company_partnership');
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->dropIndex('idx_companies_industry');
            $table->dropIndex('idx_companies_address');
            $table->dropIndex('idx_companies_industry_address');
        });
    }
};
