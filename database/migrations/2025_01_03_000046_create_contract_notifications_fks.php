<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table("contract_notifications", function (Blueprint $table) {
            $table->foreign("contract_id")
                ->references("id")
                ->on("contracts")
                ->cascadeOnDelete();
            $table->foreign("contract_notification_type_id")
                ->references("id")
                ->on("contract_notification_types")
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table("contract_notifications", function (Blueprint $table) {
            $table->dropForeign("contract_notifications_contract_id_foreign");
            $table->dropForeign("contract_notifications_contract_notification_type_id_foreign");
        });
    }
};
