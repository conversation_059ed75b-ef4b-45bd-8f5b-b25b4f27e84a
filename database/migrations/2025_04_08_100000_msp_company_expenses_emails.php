<?php

use App\Enums\Email\EmailBladeFiles;
use App\Models\Email\Email;
use App\Models\Email\EmailAllowedParameter;
use App\Services\Email\EmailService;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->companyExpensesUpcomingExpensesEmail();
        $this->companyExpensesIncreaseOverTimeframeEmail();
        $this->companyExpensesDecreaseOverTimeframeEmail();
        $this->companyExpensesIndividualRecurringExpenseIncrease();
        $this->companyExpensesIndividualRecurringExpenseDecrease();
        $this->companyExpensesIndividualRecurringExpenseDecrease();
        $this->companyExpensesWeeklySummary();
        $this->companyExpensesMonthlySummary();
        $this->companyExpensesTimeToReviewYourExpenses();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Email::whereIn("blade_file_name", [
            EmailBladeFiles::COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES_MSP,
            EmailBladeFiles::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME_MSP,
            EmailBladeFiles::COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME_MSP,
            EmailBladeFiles::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE_MSP,
            EmailBladeFiles::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE_MSP,
            EmailBladeFiles::COMPANY_EXPENSES_WEEKLY_SUMMARY_MSP,
            EmailBladeFiles::COMPANY_EXPENSES_MONTHLY_SUMMARY_MSP,
            EmailBladeFiles::COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES_MSP,
        ])->delete();
    }

    private function companyExpensesUpcomingExpensesEmail(): void
    {
        $email = EmailService::createBaseEmail(EmailBladeFiles::COMPANY_EXPENSES_UPCOMING_RECURRING_CHARGES_MSP);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "expense_count",
            "description" => "The expense count",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "defined_period",
            "description" => "The defined period",
        ]);
    }

    private function companyExpensesIncreaseOverTimeframeEmail(): void
    {
        $email = EmailService::createBaseEmail(EmailBladeFiles::COMPANY_EXPENSES_INCREASE_OVER_TIMEFRAME_MSP);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "increased_value",
            "description" => "The increase value",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "period",
            "description" => "Week, Month or Quarter",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "increased_category_and_value",
            "description" => "The increased category and the corresponding increased value",
        ]);
    }

    private function companyExpensesDecreaseOverTimeframeEmail(): void
    {
        $email = EmailService::createBaseEmail(EmailBladeFiles::COMPANY_EXPENSES_DECREASE_OVER_TIMEFRAME_MSP);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "decreased_value",
            "description" => "The decrease value",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "decreased_category_and_value",
            "description" => "The decreased category and the corresponding decreased value",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "period",
            "description" => "Week, Month or Quarter",
        ]);
    }

    private function companyExpensesIndividualRecurringExpenseIncrease(): void
    {
        $email = EmailService::createBaseEmail(EmailBladeFiles::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_INCREASE_MSP);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "charge_value",
            "description" => "The charge value",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "subscription_value",
            "description" => "The subscription value",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "increase_value",
            "description" => "The increased value",
        ]);
    }

    private function companyExpensesIndividualRecurringExpenseDecrease(): void
    {
        $email = EmailService::createBaseEmail(EmailBladeFiles::COMPANY_EXPENSES_INDIVIDUAL_RECURRING_EXPENSE_DECREASE_MSP);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "charge_value",
            "description" => "The charge value",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "subscription_value",
            "description" => "The subscription value",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "decrease_value",
            "description" => "The decreased value",
        ]);

        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "period",
            "description" => "Week, Month or Quarter",
        ]);
    }

    private function companyExpensesWeeklySummary(): void
    {
        $email = EmailService::createBaseEmail(EmailBladeFiles::COMPANY_EXPENSES_WEEKLY_SUMMARY_MSP);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "weekly_summary",
            "description" => "The weekly summary",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "decrease_increase_value",
            "description" => "The decreased or increased value",
        ]);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "category_and_value",
            "description" => "The category and value",
        ]);
    }

    private function companyExpensesMonthlySummary(): void
    {
        $email = EmailService::createBaseEmail(EmailBladeFiles::COMPANY_EXPENSES_MONTHLY_SUMMARY_MSP);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "month_name",
            "description" => "The month name",
        ]);
    }

    private function companyExpensesTimeToReviewYourExpenses(): void
    {
        $email = EmailService::createBaseEmail(EmailBladeFiles::COMPANY_EXPENSES_TIME_TO_REVIEW_YOUR_EXPENSES_MSP);
        EmailAllowedParameter::updateOrCreate([
            "email_id" => $email->id,
            "parameter" => "active_recurring_expenses",
            "description" => "The active recurring expenses",
        ]);
    }
};
