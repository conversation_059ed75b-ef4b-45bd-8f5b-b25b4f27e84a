<?php

use App\Enums\AppConfigEnum;
use App\Models\AppConfiguration;
use App\Services\Redis\RedisService;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        AppConfiguration::updateOrCreate(
            ['key' => AppConfigEnum::LARGE_TRANSACTIONS_EMAIL_THRESHOLD],
            ['value' => 1000, 'created_at' => now()]
        );
        RedisService::flushAllRedis();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        AppConfiguration::whereIn('key', [AppConfigEnum::LARGE_TRANSACTIONS_EMAIL_THRESHOLD])->delete();
        RedisService::flushAllRedis();
    }
};
