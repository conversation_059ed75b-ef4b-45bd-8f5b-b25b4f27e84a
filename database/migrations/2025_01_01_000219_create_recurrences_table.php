<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("recurrences", function (Blueprint $table) {
            $table->id();
            $table->string("key", 100)->unique();
            $table->string("name", 255)->unique();
            $table->string("description", 255)->nullable();
            $table->bigInteger("order");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("recurrences");
    }
};
