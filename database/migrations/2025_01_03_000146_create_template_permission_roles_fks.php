<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table("template_permission_roles", function (Blueprint $table) {
            $table->foreign("permission_group_id")
                ->references("id")
                ->on("permission_groups")
                ->cascadeOnDelete();
            $table->foreign("template_role_id")
                ->references("id")
                ->on("template_roles")
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table("template_permission_roles", function (Blueprint $table) {
            $table->dropForeign("template_permission_roles_permission_group_id_foreign");
            $table->dropForeign("template_permission_roles_template_role_id_foreign");
        });
    }
};
