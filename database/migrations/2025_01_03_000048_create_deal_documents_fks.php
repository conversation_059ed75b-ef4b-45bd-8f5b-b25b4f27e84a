<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table("deal_documents", function (Blueprint $table) {
            $table->foreign("company_id")
                ->references("id")
                ->on("companies")
                ->cascadeOnDelete();
            $table->foreign("deal_id")
                ->references("id")
                ->on("deals")
                ->cascadeOnDelete();
            $table->foreign("user_id")
                ->references("id")
                ->on("users")
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table("deal_documents", function (Blueprint $table) {
            $table->dropForeign("deal_documents_company_id_foreign");
            $table->dropForeign("deal_documents_deal_id_foreign");
            $table->dropForeign("deal_documents_user_id_foreign");
        });
    }
};
