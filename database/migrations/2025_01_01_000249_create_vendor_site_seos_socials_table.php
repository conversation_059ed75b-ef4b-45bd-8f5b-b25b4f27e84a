<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("vendor_site_seos_socials", function (Blueprint $table) {
            $table->id();
            $table->bigInteger("vendor_site_seos_id")->index();
            $table->string("name", 30);
            $table->string("url", 2048)->nullable();
            $table->string("image_url", 2048)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("vendor_site_seos_socials");
    }
};
