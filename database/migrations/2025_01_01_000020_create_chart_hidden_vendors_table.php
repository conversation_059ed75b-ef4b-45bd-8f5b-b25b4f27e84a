<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("chart_hidden_vendors", function (Blueprint $table) {
            $table->id();
            $table->string("channel_chart_enum_key", 255);
            $table->bigInteger("company_id");
            $table->timestamps();

            $table->unique(["company_id", "channel_chart_enum_key"]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("chart_hidden_vendors");
    }
};
