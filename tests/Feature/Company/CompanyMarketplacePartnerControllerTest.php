<?php

namespace Tests\Feature\Company;

use Tests\TestCase;

class CompanyMarketplacePartnerControllerTest extends TestCase
{
    private string $showAll = 'api/v1/company/{company}/marketplace';
    private string $distributors = 'api/v1/company/{company}/distributors';
    private string $store = 'api/v1/company/{company}/marketplace/store';
    private string $update = 'api/v1/company/{company}/marketplace/update';
    private string $delete = 'api/v1/company/{company}/marketplace/delete';
    private string $addCSV = 'api/v1/company/{company}/marketplace/add/csv';
    private string $addCSVStatus = 'api/v1/company/{company}/marketplace/add/csv/status';
    private string $addBulk = 'api/v1/company/{company}/marketplace/add/bulk';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->showAll));
        $this->assertTrue($this->checkRouteExists($this->distributors));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->update));
        $this->assertTrue($this->checkRouteExists($this->delete));
        $this->assertTrue($this->checkRouteExists($this->addCSV));
        $this->assertTrue($this->checkRouteExists($this->addCSVStatus));
        $this->assertTrue($this->checkRouteExists($this->addBulk));
    }
}
