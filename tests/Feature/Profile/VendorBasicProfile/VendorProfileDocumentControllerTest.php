<?php

namespace Tests\Feature\Profile\VendorBasicProfile;

use Tests\TestCase;

class VendorProfileDocumentControllerTest extends TestCase
{
    private string $delete = 'api/v1/profiles/vendor-basic/{companyId}/document/delete';

    private string $showAll = 'api/v1/profiles/vendor-basic/{companyId}/document';

    private string $store = 'api/v1/profiles/vendor-basic/{companyId}/document/store';

    private string $update = 'api/v1/profiles/vendor-basic/{companyId}/document/{document}/update';

    private string $download = 'api/v1/profiles/vendor-basic/{companyId}/document/{document}';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->delete));
        $this->assertTrue($this->checkRouteExists($this->showAll));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->update));
        $this->assertTrue($this->checkRouteExists($this->download));
    }
}
