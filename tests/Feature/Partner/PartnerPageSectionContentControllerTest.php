<?php

namespace Tests\Feature\Partner;

use Tests\TestCase;

class PartnerPageSectionContentControllerTest extends TestCase
{
    private string $show = 'api/v1/partner/page/{partnerPage}/section/content';
    private string $store = 'api/v1/partner/page/{partnerPage}/section/content/store';
    private string $delete = 'api/v1/partner/page/{partnerPage}/section/content/delete';
    private string $toggleHide = 'api/v1/partner/page/{partnerPage}/section/content/toggle-hide';
    private string $searchBlog = 'api/v1/partner/page/{partnerPage}/search/blog';
    private string $searchAssets = 'api/v1/partner/page/{partnerPage}/search/assets';
    private string $searchDocument = 'api/v1/partner/page/{partnerPage}/search/document';
    private string $searchTemplate = 'api/v1/partner/page/{partnerPage}/search/template';
    private string $searchVideo = 'api/v1/partner/page/{partnerPage}/search/video';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->show));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->delete));
        $this->assertTrue($this->checkRouteExists($this->toggleHide));
        $this->assertTrue($this->checkRouteExists($this->searchBlog));
        $this->assertTrue($this->checkRouteExists($this->searchAssets));
        $this->assertTrue($this->checkRouteExists($this->searchDocument));
        $this->assertTrue($this->checkRouteExists($this->searchTemplate));
        $this->assertTrue($this->checkRouteExists($this->searchVideo));
    }
}
