<?php

namespace Partner;

use Tests\TestCase;

class PartnerAssetsControllerTest extends TestCase
{
    private string $byMsp = 'api/v1/partner/page/{owner}/asset/by-msp';

    private string $store = 'api/v1/partner/page/{partnerPage}/asset/store';

    private string $deleteAsset = 'api/v1/partner/page/{partnerAsset}/asset/delete';

    private string $delete = 'api/v1/partner/page/{partnerAsset}/asset/{partnerMedia}/delete';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->byMsp));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->deleteAsset));
        $this->assertTrue($this->checkRouteExists($this->delete));
    }
}
