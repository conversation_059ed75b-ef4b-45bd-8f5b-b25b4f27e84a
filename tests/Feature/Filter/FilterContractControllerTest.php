<?php

namespace Tests\Feature\Filter;

use Tests\TestCase;

class FilterContractControllerTest extends TestCase
{
    private string $getAllContractTypes = 'api/v1/filters/contracts/types';
    private string $getAllContractAgreements = 'api/v1/filters/contracts/agreements';
    private string $getAllContractBillingTypes = 'api/v1/filters/contracts/billing-types';
    private string $getAllContractNotificationTypes = 'api/v1/filters/contracts/notification-types';
    private string $getAllContractBillingTypeOptions = 'api/v1/filters/contracts/billing-type-options';
    private string $getClientVendors = 'api/v1/filters/contracts/client-vendors';
    private string $getClientProducts = 'api/v1/filters/contracts/client-products';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->getAllContractTypes));
        $this->assertTrue($this->checkRouteExists($this->getAllContractAgreements));
        $this->assertTrue($this->checkRouteExists($this->getAllContractBillingTypes));
        $this->assertTrue($this->checkRouteExists($this->getAllContractNotificationTypes));
        $this->assertTrue($this->checkRouteExists($this->getAllContractBillingTypeOptions));
        $this->assertTrue($this->checkRouteExists($this->getClientVendors));
        $this->assertTrue($this->checkRouteExists($this->getClientProducts));
    }
}
