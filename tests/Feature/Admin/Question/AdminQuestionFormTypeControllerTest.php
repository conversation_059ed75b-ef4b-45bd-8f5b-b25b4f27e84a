<?php

namespace Admin\Question;

use Tests\TestCase;

class AdminQuestionFormTypeControllerTest extends TestCase
{
    private string $showAll = 'api/v1/admin/questions/questionformtype';

    private string $store = 'api/v1/admin/questions/questionformtype/store';

    private string $delete = 'api/v1/admin/questions/questionformtype/delete';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->showAll));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->delete));
    }
}
