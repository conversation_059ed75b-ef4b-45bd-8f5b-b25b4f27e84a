<?php

namespace Admin\Question;

use Tests\TestCase;

class AdminQuestionOptionControllerTest extends TestCase
{
    private string $showAll = 'api/v1/admin/questions/{question}/options';

    private string $store = 'api/v1/admin/questions/{question}/options/store';

    private string $update = 'api/v1/admin/questions/{question}/options/update';

    private string $delete = 'api/v1/admin/questions/{question}/options/delete';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->showAll));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->update));
        $this->assertTrue($this->checkRouteExists($this->delete));
    }
}
