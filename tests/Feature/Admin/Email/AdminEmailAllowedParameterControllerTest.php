<?php

namespace Tests\Feature\Admin\Email;

use Tests\TestCase;

class AdminEmailAllowedParameterControllerTest extends TestCase
{
    private string $showAll = 'api/v1/admin/email/allowed-parameter';

    private string $store = 'api/v1/admin/email/allowed-parameter/store';

    private string $update = 'api/v1/admin/email/allowed-parameter/update';

    private string $delete = 'api/v1/admin/email/allowed-parameter/delete';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->showAll));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->update));
        $this->assertTrue($this->checkRouteExists($this->delete));
    }
}
