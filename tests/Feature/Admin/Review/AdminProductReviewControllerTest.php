<?php

namespace Admin\Review;

use Tests\TestCase;

class AdminProductReviewControllerTest extends TestCase
{
    private string $loadProductReviews = 'api/v1/admin/review/products';

    private string $exportToCSV = 'api/v1/admin/review/products/export-to-csv';

    private string $loadProductReviewsReport = 'api/v1/admin/reports/product/review';

    private string $exportCountOfReviewsByVendorToCsv = 'api/v1/admin/reports/product/review/export-count-of-reviews-by-vendor-to-csv';

    private string $exportCountOfReviewsByProductToCsv = 'api/v1/admin/reports/product/review/export-count-of-reviews-by-product-to-csv';

    private string $moveReviews = 'api/v1/admin/review/products/move';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->loadProductReviews));
        $this->assertTrue($this->checkRouteExists($this->exportToCSV));
        $this->assertTrue($this->checkRouteExists($this->loadProductReviewsReport));
        $this->assertTrue($this->checkRouteExists($this->exportCountOfReviewsByVendorToCsv));
        $this->assertTrue($this->checkRouteExists($this->exportCountOfReviewsByProductToCsv));
        $this->assertTrue($this->checkRouteExists($this->moveReviews));
    }
}
