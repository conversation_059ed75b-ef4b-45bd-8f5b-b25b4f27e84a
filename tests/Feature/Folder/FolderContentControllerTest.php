<?php

namespace Tests\Feature\Folder;

use Tests\TestCase;

class FolderContentControllerTest extends TestCase
{
    private string $uriGetAll = 'api/v1/folder/{company}/content';

    private string $uriStore = 'api/v1/folder/{company}/content/store';

    private string $uriDelete = 'api/v1/folder/{company}/content/delete';

    private string $uriStoreMultiple = 'api/v1/folder/{company}/content/store-multiple';

    private string $uriDownload = 'api/v1/folder/{company}/content/download';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->uriGetAll));
        $this->assertTrue($this->checkRouteExists($this->uriStore));
        $this->assertTrue($this->checkRouteExists($this->uriDelete));
        $this->assertTrue($this->checkRouteExists($this->uriStoreMultiple));
        $this->assertTrue($this->checkRouteExists($this->uriDownload));
    }
}
