<?php

return [

    'disks' => [
        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => env('APP_DEBUG', false),
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL') . '/storage',
            'visibility' => 'public',
            'throw' => env('APP_DEBUG', false),
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => env('APP_DEBUG', false),
        ],

        'backBlazePublic' => [
            'driver' => 's3',
            'key' => env('AWS_PUBLIC_ACCESS_KEY_ID'),
            'secret' => env('AWS_PUBLIC_SECRET_ACCESS_KEY'),
            'region' => env('AWS_PUBLIC_DEFAULT_REGION'),
            'bucket' => env('AWS_PUBLIC_BUCKET'),
            'endpoint' => env('AWS_PUBLIC_ENDPOINT'),
        ],

        'backBlazeBlog' => [
            'driver' => 's3',
            'key' => env('BB_PUBLIC_BLOG_ACCESS_KEY_ID'),
            'secret' => env('BB_PUBLIC_BLOG_SECRET_ACCESS_KEY'),
            'region' => env('BB_PUBLIC_BLOG_DEFAULT_REGION'),
            'bucket' => env('BB_PUBLIC_BLOG_BUCKET'),
            'endpoint' => env('BB_PUBLIC_BLOG_ENDPOINT'),
        ],

        'backBlazeSitemap' => [
            'driver' => 's3',
            'key' => env('BB_PUBLIC_SITEMAP_ACCESS_KEY_ID'),
            'secret' => env('BB_PUBLIC_SITEMAP_SECRET_ACCESS_KEY'),
            'region' => env('BB_PUBLIC_SITEMAP_DEFAULT_REGION'),
            'bucket' => env('BB_PUBLIC_SITEMAP_BUCKET'),
            'endpoint' => env('BB_PUBLIC_SITEMAP_ENDPOINT'),
            'throw' => true,
            'visibility' => 'public',
        ],

        'shared' => [
            'driver' => 'local',
            'root' => env('SHARED_FILESYSTEM_ROOT_DIR', '/var/www/mnt'),
        ],
    ],

];
