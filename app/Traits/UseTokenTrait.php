<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait UseTokenTrait
{
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->token)) {
                $model->token = hash('xxh3', Str::random(40));
            }
        });
    }

    public function refreshToken(?string $base = null)
    {
        $this->token = hash('xxh3', $base ?? Str::random(40));
    }
}
