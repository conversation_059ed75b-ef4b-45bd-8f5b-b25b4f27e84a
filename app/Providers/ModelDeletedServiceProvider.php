<?php

namespace App\Providers;

use App\Enums\ModelsToObserve;
use App\Observers\ModelDeletedObserver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;

class ModelDeletedServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        self::registerModelDeletedObserver();
    }

    private static function registerModelDeletedObserver()
    {
        //Getting all files inside the Models folder and all subfolders
        $MODELS_TO_OBSERVE = self::getAllModelsFromFolder(app_path() . '/Models');
        // Adding packages models to the list
        $MODELS_TO_OBSERVE = array_merge($MODELS_TO_OBSERVE, ModelsToObserve::getValues());

        $MODELS_TO_OBSERVE = array_unique($MODELS_TO_OBSERVE);
        //Register the observer
        foreach ($MODELS_TO_OBSERVE as $MODEL) {
            if (is_subclass_of($MODEL, Model::class)) {
                $MODEL::observe(ModelDeletedObserver::class);
            }
        }
    }

    private static function getAllModelsFromFolder($path)
    {
        $models = [];
        $files = scandir($path);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                if (is_dir($path . '/' . $file)) {
                    $models = array_merge($models, self::getAllModelsFromFolder($path . '/' . $file));
                } else {
                    $models[] = self::getModel($path . '/' . $file);
                }
            }
        }

        return $models;
    }

    public static function getModel($path)
    {
        $model = str_replace('.php', '', $path);
        $model = str_replace(app_path() . '/Models/', '', $model);
        $model = str_replace('/', '\\', $model);

        return 'App\Models\\' . $model;
    }
}
