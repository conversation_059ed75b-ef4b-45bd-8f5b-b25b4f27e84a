<?php

namespace App\Services\Review;

use App\Enums\IncentiveStatus;
use App\Enums\Review\ReviewStatus;
use App\Enums\TangoCatalogType;
use App\Models\Review\Incentive;
use App\Models\Review\ReviewAnswer;
use App\Models\Review\Tango\TangoCatalog;
use App\Services\AppConfig;
use App\Services\FeatureFlagService;
use App\Services\TangoService;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;

class IncentiveService
{
    public static function createOrUpdateIncentiveStatus($review, $loggedUser, $throwException = true)
    {
        $featureFlag = FeatureFlagService::findByName('TANGO_INTEGRATION');
        if (!$featureFlag->activated) {
            return;
        }
        $incentive = $review->incentive;
        if (!$incentive) {
            // Save default incentive region answer if not exists
            try {
                ReviewAnswerService::validateIncentiveRegionAnswerExistenceOrSaveDefault($review->id);
                $tangoIncentiveCatalog = self::getTangoCatalogForIncentiveRegion($review->id);
            } catch (ValidationException $e) {
                if ($throwException) {
                    throw $e;
                }

                return;
            }
            // get tango incentive catalog for selected region
            $incentive = Incentive::make([
                'reviewed_by' => $loggedUser->id,
                'reviewer_id' => $review->reviewer_user_id,
                'review_id' => $review->id,
                'amount' => AppConfig::loadAppConfigByKey(
                    'TANGO_DEFAULT_PAYOUT_AMOUNT',
                    config('tango.app_configs.TANGO_DEFAULT_PAYOUT_AMOUNT')
                )->value,
                'status' => IncentiveStatus::IN_REVIEW,
                'tango_catalog_id' => $tangoIncentiveCatalog->id,
            ]);
        }
        if ($incentive->status === IncentiveStatus::PAID) {
            return;
        }
        $incentive->status = match ($review->status) {
            ReviewStatus::approved => IncentiveStatus::IN_REVIEW,
            ReviewStatus::flagged => IncentiveStatus::UNPAID,
            ReviewStatus::underReview => $incentive->status,
            default => throw ValidationException::withMessages([
                config('genericMessages.error.REVIEW_STATUS_NOT_APPROVED_OR_FLAGGED'),
            ])
        };
        $incentive->reviewed_by = $loggedUser->id;
        $incentive->reviewed_at = Carbon::now();
        $incentive->save();
        $review->load('incentive.reviewed');

        return $review;
    }

    // get tango catalog id for incentive region
    public static function getTangoCatalogForIncentiveRegion($reviewId)
    {
        $regionOptionId = ReviewAnswer::where('review_id', $reviewId)
            ->whereHas('option', function ($query) {
                $query->where('key', 'LIKE', 'INCENTIVE_REGION_%')->whereNull('archived_at');
            })->first()->option?->id;

        if (!$regionOptionId) {
            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '->' . __FUNCTION__ . '::Region Option not found for review ' . $reviewId,
            ]);
        }
        $tangoCatalog = TangoCatalog::where('region_option_id', $regionOptionId)
            ->where('catalog_type', TangoCatalogType::INCENTIVES)->first();

        if (!$tangoCatalog) {
            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '->' . __FUNCTION__ . '::Tango Catalog not configured for incentive region ' . $regionOptionId,
            ]);
        }

        return $tangoCatalog;
    }

    // calculate incentive total incentive paid to user
    public static function calculateIncentivePaidToUser($reviewerId)
    {
        $incentivePaidToUser = Incentive::where('reviewer_id', $reviewerId)
            ->where('status', IncentiveStatus::PAID)
            ->sum('amount');

        return $incentivePaidToUser;
    }

    public static function incentiveInReview($request, $review, $loggedUser)
    {
        self::checkReviewFlagged($review);
        $review->incentive->status = IncentiveStatus::IN_REVIEW;
        $review->incentive->rejection_reason = null;
        $review->incentive->reviewed_by = $loggedUser->id;
        $review->incentive->reviewed_at = Carbon::now();
        $review->incentive->save();
    }

    public static function incentiveUnpaid($request, $review, $loggedUser)
    {
        $review->incentive->status = IncentiveStatus::UNPAID;
        $review->incentive->rejection_reason = $request->rejection_reason;
        $review->incentive->reviewed_by = $loggedUser->id;
        $review->incentive->reviewed_at = Carbon::now();
        $review->incentive->save();
        $review->update(['incentivize' => false]);
    }

    public static function incentivePaid($request, $review, $loggedUser)
    {
        self::checkReviewFlagged($review);
        self::checkReviewIsUnderReview($review);
        if (!$request->has('override_limit') || $request->override_limit === false) {
            self::validatePayoutLimits($request, $review);
        }
        $incentive = $review->incentive;
        TangoService::verifyCatalogAndAccountExistence($incentive->tangoCatalog);
        if ($request->has('amount')) {
            $incentive->amount = $request->amount;
        }
        $tangoOrderParameter = TangoService::setOrderParameterFromReview($review);
        $tangoOrderResponse = TangoService::sendOrder($tangoOrderParameter);
        if (!$tangoOrderResponse) {
            throw ValidationException::withMessages([
                config('genericMessages.error.TANGO_ORDER_FAILED'),
            ]);
        }
        $incentive->tango_reference_order_id = $tangoOrderResponse['referenceOrderID'];
        if (
            isset($tangoOrderResponse['amountCharged']['value']) &&
            $incentive->amount != $tangoOrderResponse['amountCharged']['value']
        ) {
            $incentive->amount = $tangoOrderResponse['amountCharged']['value'];
        }
        $review->update(['incentivize' => true]);
        $incentive->status = IncentiveStatus::PAID;
        $review->incentive->rejection_reason = null;
        $incentive->reviewed_by = $loggedUser->id;
        $incentive->reviewed_at = Carbon::now();
        $incentive->paid_at = Carbon::now();
        $incentive->save();
    }

    private static function validatePayoutLimits($request, $review)
    {
        $singlePayoutLimit = AppConfig::loadAppConfigByKey(
            'TANGO_MAX_SINGLE_PAYOUT',
            config('tango.app_configs.TANGO_MAX_SINGLE_PAYOUT')
        )->value;
        if ($request->has('amount') && $request->amount > floatval($singlePayoutLimit)) {
            throw ValidationException::withMessages([
                config('genericMessages.error.TANGO_MAX_SINGLE_PAYOUT_EXCEEDED'),
            ]);
        }
        $maxPayoutLimit = AppConfig::loadAppConfigByKey(
            'TANGO_TOTAL_MAX_PAYOUT',
            config('tango.app_configs.TANGO_TOTAL_MAX_PAYOUT')
        )->value;
        $totalAmount = Incentive::where('reviewer_id', $review->reviewer_user_id)
            ->where('status', IncentiveStatus::PAID)
            ->sum('amount') + ($request->has('amount') ? $request->amount : 0);
        if ($totalAmount > floatval($maxPayoutLimit)) {
            throw ValidationException::withMessages([
                config('genericMessages.error.TANGO_TOTAL_MAX_PAYOUT_EXCEEDED'),
            ]);
        }
    }

    private static function checkReviewFlagged($review)
    {
        // if status is approved, and request has amount, update incentive amount
        if ($review->status === ReviewStatus::flagged) {
            throw ValidationException::withMessages([
                config('genericMessages.error.REVIEW_STATUS_NOT_APPROVED_OR_FLAGGED'),
            ]);
        }
    }

    private static function checkReviewIsUnderReview($review)
    {
        // if status is approved, and request has amount, update incentive amount

        if ($review->status === ReviewStatus::underReview) {
            throw ValidationException::withMessages([
                config('genericMessages.error.REVIEW_STATUS_NOT_APPROVED_OR_FLAGGED'),
            ]);
        }
    }
}
