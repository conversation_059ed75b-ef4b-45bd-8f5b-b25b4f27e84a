<?php

namespace App\Services;

use App\Models\Tags;
use Illuminate\Support\Facades\DB;

class TagService
{
    public static function getUsedByVideoCountList()
    {
        $query = "SELECT tags.id::text as tag_id, count(media.id) FROM media
            CROSS JOIN LATERAL jsonb_array_elements(custom_properties->'tags') as tag
            left join tags on tag::jsonb::text = concat('\"',tags.id::text,'\"')
            group by tag_id";

        return collect(DB::select($query))->keyBy('tag_id');
    }

    public static function getMediasTagsUsageQuery(): string
    {
        return "SELECT count(media.id)   AS total_usage_count,
                         t.id            AS id,
                         t.name          AS name
                FROM media
                    CROSS JOIN LATERAL jsonb_array_elements(custom_properties -> 'tags') AS tag
                    LEFT JOIN tags t ON tag::jsonb::TEXT = concat('\"', t.id::TEXT, '\"')
                WHERE tag::TEXT IS NOT NULL
                    AND t.id IS NOT NULL
                GROUP BY t.id, t.name
                ORDER BY total_usage_count DESC";
    }

    public static function getTopVideoUsedTags($itemsPerPage)
    {
        $mediasTagsUsageQuery = self::getMediasTagsUsageQuery();

        $topTagsQuery = <<<EOT
            SELECT sum(total_usage_count) AS total_usage_count, id, name
            FROM ({$mediasTagsUsageQuery})
            GROUP BY id, name
            ORDER BY total_usage_count DESC
                LIMIT {$itemsPerPage}
            EOT;

        return Tags::hydrate(DB::select($topTagsQuery));
    }
}
