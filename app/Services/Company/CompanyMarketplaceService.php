<?php

namespace App\Services\Company;

use App\Models\Company\CompanyMarketplacePartner;
use Illuminate\Database\QueryException;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class CompanyMarketplaceService
{
    /**
     * Adds a new partner to the company's marketplace.
     *
     * @param  int  $company_id  The ID of the company (distributor) adding the partner.
     * @param  int  $partner_id  The ID of the partner to be added.
     * @param  string  $description  The description of the partnership.
     * @param  bool  $ignoreDuplicates  Whether to ignore duplicate entries. Default is false.
     *
     * @throws ValidationException If a duplicate entry is found and $ignoreDuplicates is false.
     */
    public static function addNewPartner(
        int $company_id, int $partner_id, string $description, ?string $external_url = null,
        bool $ignoreDuplicates = false): void
    {
        try {
            CompanyMarketplacePartner::create([
                'distributor_id' => $company_id,
                'partner_id' => $partner_id,
                'description' => $description,
                'external_url' => $external_url,
            ]);
        } catch (QueryException $ex) {
            if ($ignoreDuplicates) {
                return;
            }
            $searchForMessage = 'duplicate key value violates unique constraint';
            if (Str::contains($ex->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.COMPANY_WITH_CATEGORY_AND_PRODUCT_ALREADY_ADDED_TO_MARKETPLACE'),
                ]);
            }

            throw $ex;
        }
    }
}
