<?php

namespace App\Services\Plaid;

use App\Enums\Lookup\LookupOptionsEnum;
use App\Enums\Lookup\Values\Plaid\PlaidNotifyAlertPeriodValueEnum;
use App\Enums\Lookup\Values\Plaid\PlaidNotifyAlertTypesValueEnum;
use App\Http\Resources\Plaid\Alert\PlaidAlertNotificationResource;
use App\Jobs\Plaid\PlaidCalculateExpenses;
use App\Models\Company\Company;
use App\Services\LookupService\LookupService;
use Illuminate\Support\Facades\Log;

class PlaidAlertService
{
    private const DEFAULT_ALERT_PERIOD = PlaidNotifyAlertPeriodValueEnum::MONTHLY;

    public static function setDefaultAlertSetting(Company $company): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;

        $alerts = $company->plaidNotifyAlerts();
        if (!$alerts->count()) {
            Log::debug($source . ':: Set up default alter setting: COMPANY ID: ' . $company->id);

            $alertsNotifications = LookupService::getAllLookupOptionValueIds(LookupOptionsEnum::PLAID_NOTITY_ALERTS);
            $monthlyPeriodId = LookupService::getLookupOptionValueId(LookupOptionsEnum::PLAID_NOTITY_ALERT_PERIODS, self::DEFAULT_ALERT_PERIOD);
            $weeklyPeriodId = LookupService::getLookupOptionValueId(LookupOptionsEnum::PLAID_NOTITY_ALERT_PERIODS, PlaidNotifyAlertPeriodValueEnum::WEEKLY);

            $upcomingAlertTypeId = LookupService::getLookupOptionValueId(LookupOptionsEnum::PLAID_NOTITY_ALERTS, PlaidNotifyAlertTypesValueEnum::UPCOMING_EXPENSES);

            foreach ($alertsNotifications as $alertNotification) {
                $company->plaidNotifyAlerts()->updateOrcreate([
                    'type_id' => $alertNotification->id,
                    'period_id' => $upcomingAlertTypeId === $alertNotification->id ? $weeklyPeriodId : $monthlyPeriodId,
                    'active' => true,
                ]);
            }
        }
    }

    public static function refreshCompanyAlertNotification(Company $company): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;

        Log::debug($source . ':: Company ID: ' . $company->id);

        self::setDefaultAlertSetting($company);
        PlaidCalculateExpenses::dispatch(self::DEFAULT_ALERT_PERIOD, $company->plaidNotifyAlerts);
    }

    public static function getCompanyNotifications(Company $company, $request)
    {
        return $company->plaidCompanyNotifications()
            ->select('plaid_company_notifications.extra_info',
                'plaid_company_notifications.created_at',
                'plaid_company_notifications.subscription_id',
                'plaid_company_notifications.type_id',
                'plaid_company_notifications.period_id',
            )
            ->leftJoin('plaid_subscriptions', 'plaid_subscriptions.id', '=', 'plaid_company_notifications.subscription_id')
            ->leftJoin('products', 'products.id', '=', 'plaid_subscriptions.product_id')
            ->leftJoin('companies as vendor_companies', 'vendor_companies.id', '=', 'plaid_subscriptions.vendor_id')
            ->when($request->has('search_word'), function ($query) use ($request) {
                $query->where('products.name', 'ilike', '%' . $request->input('search_word') . '%')
                    ->orWhere('plaid_subscriptions.merchant_name', 'ilike', '%' . $request->input('search_word') . '%')
                    ->orWhere('plaid_subscriptions.description', 'ilike', '%' . $request->input('search_word') . '%')
                    ->orWhere('vendor_companies.name', 'ilike', '%' . $request->input('search_word') . '%');
            })->when($request->has('alert_type'),  function ($query) use ($request) {
                $query->whereIn('plaid_company_notifications.type_id', $request->alert_type);
            })->when($request->has('period'),  function ($query) use ($request) {
                $query->whereIn('plaid_company_notifications.period_id', $request->period);
            })->when($request->has('vendor'),  function ($query) use ($request) {
                $query->whereIn('plaid_subscriptions.vendor_id', $request->vendor);
            })->when($request->has('date.start_date') && $request->has('date.end_date'), function ($query) use ($request) {
                $query->whereBetween('plaid_company_notifications.created_at', [$request->input('date.start_date'), $request->input('date.end_date')]);
            })->with(PlaidAlertNotificationResource::ALL_RELATIONSHIP_LOAD);
    }
}
