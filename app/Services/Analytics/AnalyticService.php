<?php

namespace App\Services\Analytics;

use App\Enums\AnalyticAction;
use App\Enums\AnalyticSubjectType;
use App\Helpers\UtilityHelper;
use App\Http\Requests\Analytics\AnalyticsStoreRequest;
use App\Models\Analytics\Analytics;
use App\Models\Blog\Blog;
use App\Models\Company\Company;
use App\Models\MediaGallery;
use App\Models\Product;
use App\Models\ProductFeature;
use App\Models\Review\Review;
use App\Models\ShoutOut\ShoutOut;
use App\Models\User;
use App\Services\Partner\PartnerPageEngagementService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AnalyticService
{
    /**
     * @throws ValidationException
     */
    public static function store(AnalyticsStoreRequest $request)
    {
        $action = $request->input('action');

        return match ($action) {
            AnalyticAction::partnerPortalView, AnalyticAction::partnerPortalChatOpen,
            AnalyticAction::partnerPortalContentDownload, AnalyticAction::partnerPortalContentClick => PartnerPageEngagementService::processPartnerPortalAction($request),
            AnalyticAction::referredRegistration, AnalyticAction::referredReview,
            AnalyticAction::referredReturned, AnalyticAction::referredStack => ReferralAnalyticService::store($request),
            AnalyticAction::videoViews => VideoViewsService::storeVideoView($request),
            AnalyticAction::like, AnalyticAction::dislike => LikeService::storeLikeOrDislike($request),
            AnalyticAction::advertisementStore, AnalyticAction::advertisementDelete,
            AnalyticAction::advertisementClick, AnalyticAction::advertisementUpdate,
            AnalyticAction::advertisementHover,
            AnalyticAction::advertisementView => AdvertisementAnalyticService::store($request),
            AnalyticAction::pageView, AnalyticAction::pageLeave => UserAnalyticService::store($request),
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for action: ' . $action,
            ]),
        };
    }

    public static function count($action, $authorId = null, $subjectId = null)
    {
        $analyticsQuery = Analytics::where('action', $action);
        if ($authorId) {
            $analyticsQuery->where('author_id', $authorId);
        }
        if ($subjectId) {
            $analyticsQuery->where('subject_id', $subjectId);
        }

        return $analyticsQuery->count();
    }

    public static function getCountsForActionByIds($action, $collectionIds)
    {
        return Analytics::select('subject_id', DB::raw('count(*)'))
            ->where('action', $action)
            ->whereIn('subject_id', $collectionIds)
            ->groupBy('subject_id')
            ->orderBy('count')
            ->get()
            ->keyBy('subject_id');
    }

    public static function findViewsCounts($collection)
    {
        if ($collection->isEmpty()) {
            return collect();
        }
        $videoIds = $collection->pluck('id');
        $query = DB::table('media')
            ->select([
                'media.id',
                DB::raw("(select count(id) from analytics where subject_id = media.id and action = 'videoViews') as total_views"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and author_id is null and action = 'videoViews') as anonymous_views"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and author_id is not null and action = 'videoViews') as logged_in_user_views"),
            ]);
        $query->whereIn('media.id', $videoIds->toArray());

        return $query->get()->keyBy('id');
    }

    /**
     * @return Model|Builder|Collection|object|null
     */
    public static function findMediaLikesOrDislikesCounts($collection, $actionType)
    {
        $allowActionTypes = [AnalyticAction::like, AnalyticAction::dislike];
        if (!in_array($actionType, $allowActionTypes)) {
            return collect();
        }
        $videoIds = $collection->pluck('id');
        $query = DB::table('media')
            ->select([
                'media.id',
                DB::raw("(select count(id) from analytics where subject_id = media.id and action = '" . $actionType . "' and author_id is not null) as " . $actionType . 's'),
            ]);
        $query->whereIn('media.id', $videoIds->toArray());

        return $query->get()->keyBy('id');
    }

    /**
     * @return Model|Builder|object|null
     */
    public static function findViewsViewDetailsByVideoId(Media $video)
    {
        $query = DB::table('media')
            ->select([
                'companies.name', 'companies.friendly_url as company_friendly_url', 'companies.profile_vendor_handle',
                'users.first_name', 'users.friendly_url as user_friendly_url', 'users.handle',
                DB::raw("(select count(distinct(author_id)) from analytics where subject_id = media.id and action = 'videoViews') as total_viewers"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and action = 'videoViews') as total_views"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and author_id is null and action = 'videoViews') as anonymous_views"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and author_id is not null and action = 'videoViews') as logged_in_user_views"),
                DB::raw("(Select avg((custom_properties->'view_time')::jsonb::integer) from analytics where subject_id = media.id ) as average_watch_time"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and action = 'like') as likes"),
            ])
            ->leftJoin('companies', 'media.model_id', 'companies.id')
            ->leftJoin('users', 'media.model_id', 'users.id');
        $query->where('media.id', $video->id);

        return $query->first();
    }

    public static function findViewsViewDetailsByVideoAndDates(Media $video, $startDate, $endDate)
    {
        $formattedStartDate = $startDate->format('Y-m-d H:i:s');
        $formattedEndDate = $endDate->format('Y-m-d H:i:s');

        $query = DB::table('media')
            ->select([
                'companies.name', 'companies.friendly_url as company_friendly_url', 'companies.profile_vendor_handle',
                'users.first_name', 'users.friendly_url as user_friendly_url', 'users.handle',
                DB::raw("(select count(distinct(author_id)) from analytics where subject_id = media.id and action = 'videoViews' and created_at between '" . $formattedStartDate . "' and '" . $formattedEndDate . "') as total_viewers"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and action = 'videoViews' and created_at between '" . $formattedStartDate . "' and '" . $formattedEndDate . "') as total_views"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and action = 'videoViews' and author_id is null and created_at between '" . $formattedStartDate . "' and '" . $formattedEndDate . "') as anonymous_views"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and action = 'videoViews' and author_id is not null and created_at between '" . $formattedStartDate . "' and '" . $formattedEndDate . "') as logged_in_user_views"),
                DB::raw("(Select avg((custom_properties->'view_time')::jsonb::integer) from analytics where subject_id = media.id and created_at between '" . $formattedStartDate . "' and '" . $formattedEndDate . "') as average_watch_time"),
                DB::raw("(select count(id) from analytics where subject_id = media.id and action = 'like' and created_at between '" . $formattedStartDate . "' and '" . $formattedEndDate . "') as likes"),
            ])
            ->leftJoin('companies', 'media.model_id', 'companies.id')
            ->leftJoin('users', 'media.model_id', 'users.id');
        $query->where('media.id', $video->id);

        return $query->first();
    }

    public static function findViewsViewCSVDataByVideoAndDates(Media $video, $startDate, $endDate)
    {
        $formattedStartDate = $startDate->format('Y-m-d H:i:s');
        $formattedEndDate = $endDate->format('Y-m-d H:i:s');

        $query = DB::table('analytics')
            ->select([
                'analytics.created_at as view_date',
                'users.first_name as user_first_name', 'users.last_name as user_last_name', 'users.friendly_url as user_friendly_url', 'users.handle as user_handle',
                DB::raw("(CASE
                        WHEN custom_properties->'is_desktop'::string = 'true' THEN 'desktop'
                        WHEN custom_properties->'is_tablet'::string = 'true' THEN 'tablet'
                        WHEN custom_properties->'is_mobile'::string = 'true' THEN 'mobile'
                        ELSE 'unknown'
                        END
                        ) as device_type,
                    custom_properties->'view_time' as view_time,
                       custom_properties->'device' as device,
                       custom_properties->'browser' as browser,
                       custom_properties->'browser_version' as browser_version,
                       custom_properties->'platform' as platform,
                       custom_properties->'platform_version' as platform_version"),
            ])
            ->leftJoin('users', 'analytics.author_id', 'users.id');
        $query->where('analytics.subject_id', $video->id);
        $query->where('analytics.action', 'videoViews');
        $query->whereRaw("analytics.created_at between '{$formattedStartDate}' and '{$formattedEndDate}'");

        return $query->get();
    }

    public static function appendLikesCounts(Collection $collection, $loggedUserLikes): Collection
    {
        $collection = $collection->filter();
        if ($collection->count() === 0) {
            return $collection;
        }
        $modelIds = $collection->pluck('id');
        $likesCount = AnalyticService::getCountsForActionByIds(AnalyticAction::like, $modelIds);
        $collection->transform(function ($m) use ($likesCount, $loggedUserLikes) {
            if (!empty($m) && !empty($m->id)) {
                $m->likes_count = $likesCount->get($m->id)->count ?? 0;
                $m->user_has_liked = $loggedUserLikes->where('subject_id', '=', $m->id)->count() > 0;
            }

            return $m;
        });

        return $collection;
    }

    public static function appendCommentsCounts(Collection $collection, $loggedUserId): Collection
    {
        $collection = $collection->filter();
        if ($collection->count() === 0) {
            return $collection;
        }
        $modelIds = $collection->pluck('id')->toArray();
        $commentsCount = LikeService::getUserCommentsDataByIdList($modelIds, $loggedUserId);
        $collection->transform(function ($m) use ($commentsCount) {
            if (!empty($m) && !empty($m->id)) {
                $m->comments_count = $commentsCount->get($m->id)?->comments_count ?? 0;
                $m->user_has_commented = (bool)$commentsCount->get($m->id)?->user_has_commented ?? false;
            }

            return $m;
        });

        return $collection;
    }

    public static function storeLoginAction(Request $request, User $author)
    {
        $analyticLoginAction = new Analytics();
        $analyticLoginAction->action = AnalyticAction::login;
        $analyticLoginAction->author_id = $author->id;
        $analyticLoginAction->author_type = User::class;
        $analyticLoginAction->subject_id = $author->id;
        $analyticLoginAction->subject_type = User::class;
        $analyticLoginAction->custom_properties = [
            'ip_address' => self::getIpAddressFromRequest($request),
        ];
        $analyticLoginAction->save();
    }

    public static function getIpAddressFromRequest(Request $request)
    {
        $ipAddresses = $request->header('X-Forwarded-For');
        if (empty($ipAddresses)) {
            return $request->ip();
        }

        $ipAddressesArray = explode(',', $ipAddresses);

        return array_shift($ipAddressesArray);
    }

    public static function getLoginListByUserId($request, $resultsPerPage)
    {
        $query = Analytics::where('subject_id', $request->id)
            ->where('action', AnalyticAction::login)
            ->select(['custom_properties->ip_address as ip_address', 'created_at']);

        return UtilityHelper::getSearchRequestQueryResults($request, $query, 0);
    }

    public static function getPartnerPortalAnalyticActions(): array
    {
        return [
            AnalyticAction::partnerPortalView, AnalyticAction::partnerPortalContentDownload,
            AnalyticAction::partnerPortalContentClick, AnalyticAction::partnerPortalChatOpen,
        ];
    }

    public static function prepareUserAgent(Agent $agent)
    {
        $browser = $agent->browser();
        $platform = $agent->platform();

        return [
            'view_time' => 0,
            'is_android' => $agent->isAndroidOS(),
            'is_nexus' => $agent->isNexus(),
            'is_safari' => $agent->isSafari(),
            'is_mobile' => $agent->isMobile(),
            'is_phone' => $agent->isPhone(),
            'is_tablet' => $agent->isTablet(),
            'is_desktop' => $agent->isDesktop(),
            'device' => $agent->device(),
            'browser' => $agent->browser(),
            'browser_version' => $agent->version($browser),
            'platform' => $platform,
            'platform_version' => $agent->version($platform),
        ];
    }

    /**
     * @throws ValidationException
     */
    public static function getSubjectTypeClass($requestSubjectType): ?string
    {
        return match ($requestSubjectType) {
            AnalyticSubjectType::blog => Blog::class,
            AnalyticSubjectType::product => Product::class,
            AnalyticSubjectType::media => Media::class,
            AnalyticSubjectType::mediaGallery => MediaGallery::class,
            AnalyticSubjectType::shoutOut => ShoutOut::class,
            AnalyticSubjectType::review => Review::class,
            AnalyticSubjectType::userProfile => User::class,
            AnalyticSubjectType::vendorProfile, AnalyticSubjectType::mspProfile => Company::class,
            AnalyticSubjectType::productFeature => ProductFeature::class,
            AnalyticSubjectType::anonymousUser => 'AnonymousUser',
            default => throw ValidationException::withMessages([
                'ERROR:' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $requestSubjectType,
            ])
        };
    }

    /**
     * @throws ValidationException
     */
    public static function validateModelExistence($requestSubjectType, $modelId): void
    {
        $modelExists = match ($requestSubjectType) {
            AnalyticSubjectType::blog => Blog::where('id', $modelId)->exists(),
            AnalyticSubjectType::product => Product::where('id', $modelId)->exists(),
            AnalyticSubjectType::media => Media::where('id', $modelId)->exists(),
            AnalyticSubjectType::mediaGallery => MediaGallery::where('id', $modelId)->exists(),
            AnalyticSubjectType::shoutOut => ShoutOut::where('id', $modelId)->exists(),
            AnalyticSubjectType::review => Review::where('id', $modelId)->exists(),
            AnalyticSubjectType::userProfile => User::where('id', $modelId)->exists(),
            AnalyticSubjectType::vendorProfile, AnalyticSubjectType::mspProfile => Company::where('id', $modelId)->exists(),
            AnalyticSubjectType::productFeature => ProductFeature::where('id', $modelId)->exists(),
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $requestSubjectType,
            ])
        };

        if (!$modelExists) {
            throw ValidationException::withMessages([
                config('genericMessages.error.SUBJECT_ID_DOES_NOT_EXIST_FOR_SUBJECT_TYPE'),
            ]);
        }
    }
}
