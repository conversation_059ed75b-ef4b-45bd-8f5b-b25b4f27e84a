<?php

namespace App\Services\Contract;

use App\Enums\ActivityLogAction;
use App\Enums\Contract\BillingFrequencyLabelEnum;
use App\Enums\ModelType;
use App\Models\Category\Category;
use App\Models\Contract\ClientProduct;
use App\Models\Contract\Contract;
use App\Models\Contract\ContractAgreement;
use App\Models\Contract\ContractBillingTypeOptions;
use App\Models\Product;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Validation\ValidationException;

class ContractLogService
{
    /**
     * This service manage the logs for the contracts when performing an action
     *
     * @throws ValidationException
     * @throws Exception
     */
    public static function addActivityLog(
        string $action, Contract $contract, User $loggedUser): void
    {
        $additionalData = [];
        $subjectType = ModelType::companyContract;
        $subjectId = $contract->id;
        $contractName = $contract->product?->name ?? $contract->clientProduct?->name ?? $contract->name;
        switch ($action) {
            case ActivityLogAction::storeContract:
                $additionalData[] = [
                    'contract_name' => $contractName,
                    'event' => 'Created',
                    'description' => '',
                ];

                break;
            case ActivityLogAction::updateContract:
                self::addUpdateActivityLog($contract, $loggedUser);

                break;
            case ActivityLogAction::deleteContract:
                if ($contract->parent_id === null) {
                    $subjectType = ModelType::companies;
                    $subjectId = $contract->owner_id;
                    $additionalData[] = [
                        'contract_id' => '' . $contract->id,
                    ];
                } else {
                    $action = ActivityLogAction::deleteProductContract;
                    $subjectId = $contract->parent_id;
                    $additionalData[] = [
                        'contract_name' => $contractName,
                        'event' => 'Deleted "Product Contract"',
                        'description' => $contractName,
                    ];
                }

                break;
            default:
                throw ValidationException::withMessages([
                    'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for action:' . $action,
                ]);
        }

        foreach ($additionalData as $data) {
            ActivityLogsService::store(
                $action,
                ModelType::userType,
                $loggedUser->id,
                $subjectType,
                $subjectId,
                $data
            );
        }
    }

    /**
     * Adds activity logs for changes in the specified contract.
     *
     * @param  Contract  $contract  The contract with changes to log.
     * @param  User  $loggedUser  The user making the changes.
     */
    public static function addUpdateActivityLog(Contract $contract, User $loggedUser): void
    {
        if (!$contract->isDirty()) {
            return;
        }
        $subjectType = ModelType::companyContract;
        $subjectId = $contract->id;
        $action = ActivityLogAction::updateContract;
        $changedFields = $contract->getDirty();
        $dataToLog = [];
        foreach ($changedFields as $field => $newValue) {
            $data = match ($field) {
                'client_product_id' => self::getContractClientProductsValChangedStatusData($contract),
                'product_id' => self::getContractProductsValChangedStatusData($contract),
                'name' => self::getFieldChangedStatusData($contract, 'name', 'Name'),
                'category_id' => self::getCategoryValChangedStatusData($contract),
                'contract_agreement_id' => self::getContractAgreementValChangedStatusData($contract),
                'cost' => self::getFieldChangedStatusData($contract, 'cost', 'Cost'),
                'discount' => self::getFieldChangedStatusData($contract, 'discount', 'Discount'),
                'start_date' => self::getDateChangedStatusData($contract, 'start_date', 'Start Date'),
                'end_date' => self::getDateChangedStatusData($contract, 'end_date', 'End Date'),
                'recurrence' => self::getBillingFrequencyChangedStatusData($contract),
                'initial_payment_date' => self::getDateChangedStatusData($contract, 'initial_payment_date', 'Billing Date'),
                'notice_period' => self::getFieldChangedStatusData($contract, 'notice_period', 'Notice Period'),
                'auto_renew' => self::getBooleanValChangedStatusData($contract, 'auto_renew', 'Auto Renew'),
                'custom_properties' => self::getCustomPropertiesValChangedStatusData($contract),
                default => null
            };
            if (!is_null($data)) {
                $dataToLog = array_merge($dataToLog, $data);
            }
        }
        foreach ($dataToLog as $data) {
            ActivityLogsService::store(
                $action,
                ModelType::userType,
                $loggedUser->id,
                $subjectType,
                $subjectId,
                $data
            );
        }
    }

    /**
     * Generates an array with additional data for logging purposes.
     *
     * @param  Contract  $contract  The contract object associated with the log entry.
     * @param  string  $event  The event name that triggers the logging.
     * @param  string  $description  A description of the log entry.
     * @param  string  $fieldName  The name of the field that has changed.
     * @param  mixed  $oldValue  The old value of the field (optional).
     * @param  mixed  $newValue  The new value of the field (optional).
     * @return array An associative array
     */
    private static function getLogAditionalDataObject(
        Contract $contract,
        string $event,
        string $description,
        string $fieldName,
        mixed $oldValue = null,
        mixed $newValue = null,
    ): array {
        $response = [
            'contract_name' => $contract->product?->name ?? $contract->clientProduct?->name ?? $contract->name,
            'event' => $event,
            'description' => $description,
            'field' => $fieldName,
            'old_value' => $oldValue,
            'new_value' => $newValue,
        ];

        return $response;
    }

    /**
     * Get status data for changes in the contract client product field of a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @return array An array containing the event and description of the category and subcategory changes.
     */
    private static function getContractClientProductsValChangedStatusData(Contract $contract): array
    {
        $oldClientProductId = $contract->getOriginal('client_product_id');
        $oldClientProduct = empty($oldClientProductId) ? null : ClientProduct::select(['id', 'name'])
            ->firstWhere('id', $oldClientProductId);
        $oldClientProductName = $oldClientProduct?->name ?? 'empty';

        $newClientProductId = $contract->client_product_id;
        $newClientProduct = empty($newClientProductId) ? null : ClientProduct::select(['id', 'name'])
            ->firstWhere('id', $newClientProductId);
        $newClientProductName = $newClientProduct?->name ?? 'empty';

        $response = [
            self::getLogAditionalDataObject(
                $contract,
                'Edited "Contract Product"',
                'From ' . $oldClientProductName . ' -> ' . $newClientProductName,
                'client_product_id', $oldClientProductId, $newClientProductId
            ),
        ];

        return $response;
    }

    /**
     * Get status data for changes in the contract product field of a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @return array An array containing the event and description of the category and subcategory changes.
     */
    private static function getContractProductsValChangedStatusData(Contract $contract): array
    {
        $oldProductId = $contract->getOriginal('product_id');
        $oldProduct = empty($oldProductId) ? null : Product::select(['id', 'name'])
            ->firstWhere('id', $oldProductId);
        $oldProductName = $oldProduct?->name ?? 'empty';

        $newProductId = $contract->product_id;
        $newProduct = empty($newProductId) ? null : Product::select(['id', 'name'])
            ->firstWhere('id', $newProductId);
        $newProductName = $newProduct?->name ?? 'empty';

        $response = [
            self::getLogAditionalDataObject(
                $contract,
                'Edited "Contract Product"',
                'From ' . $oldProductName . ' -> ' . $newProductName,
                'product_id', $oldProductId, $newProductId
            ),
        ];

        return $response;
    }

    /**
     * Get status data for changes in the custom properties of a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @return array An array containing the events and descriptions of the changes in custom properties.
     */
    private static function getCustomPropertiesValChangedStatusData(Contract $contract): array
    {
        if (!$contract->isDirty('custom_properties')) {
            return [];
        }
        $oldValues = $contract->getOriginal('custom_properties') ?? [];
        $newValues = $contract->custom_properties ?? [];
        $allKeys = array_unique(array_merge(array_keys($oldValues), array_keys($newValues)));
        $response = [];
        foreach ($allKeys as $key) {
            $oldValue = $oldValues[$key] ?? null;
            $newValue = $newValues[$key] ?? null;
            if ($oldValue === $newValue) {
                continue;
            }
            $data = match ($key) {
                'details' => self::getLogAditionalDataObject(
                    $contract,
                    'Edited "Details"',
                    'From ' . ($oldValue ?? 'empty') . ' -> ' . ($newValue ?? 'empty'),
                    'custom_properties->>details', $oldValue, $newValue
                ),
                'amount_purchased' => self::getLogAditionalDataObject(
                    $contract,
                    'Edited "Amount Purchased"',
                    'From ' . ($oldValue ?? 'empty') . ' -> ' . ($newValue ?? 'empty'),
                    'custom_properties->>amount_purchased', $oldValue, $newValue
                ),
                'per' => (function (?string $oldValue, ?string $newValue) use ($contract) {
                    $oldBillingTypeOpt = is_null($oldValue) ? 'empty' : ContractBillingTypeOptions::find($oldValue)->name;
                    $newBillingTypeOpt = is_null($newValue) ? 'empty' : ContractBillingTypeOptions::find($newValue)->name;

                    return self::getLogAditionalDataObject(
                        $contract,
                        'Edited "Per"',
                        'From ' . $oldBillingTypeOpt . ' -> ' . $newBillingTypeOpt,
                        'custom_properties->>per', $oldValue, $newValue
                    );
                })($oldValue, $newValue),
                'unit_cost' => self::getLogAditionalDataObject(
                    $contract,
                    'Edited "Unit Cost"',
                    'From ' . ($oldValue ?? 'empty') . ' -> ' . ($newValue ?? 'empty'),
                    'custom_properties->>unit_cost', $oldValue, $newValue
                ),
                default => null
            };
            if (!is_null($data)) {
                $response[] = $data;
            }
        }

        return $response;
    }

    /**
     * Get status data for changes in the contract agrrement field of a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @return array An array containing the event and description of the category and subcategory changes.
     */
    private static function getContractAgreementValChangedStatusData(Contract $contract): array
    {
        $oldAgreementId = $contract->getOriginal('contract_agreement_id');
        $oldAgreement = empty($oldAgreementId) ? null : ContractAgreement::select(['id', 'name'])
            ->firstWhere('id', $oldAgreementId);
        $oldAgreementName = $oldAgreement?->name ?? 'empty';

        $newAgreementId = $contract->contract_agreement_id;
        $newAgreement = empty($newAgreementId) ? null : ContractAgreement::select(['id', 'name'])
            ->firstWhere('id', $newAgreementId);
        $newAgreementName = $newAgreement?->name ?? 'empty';

        $response = [
            self::getLogAditionalDataObject(
                $contract,
                'Edited "Contract Term"',
                'From ' . $oldAgreementName . ' -> ' . $newAgreementName,
                'contract_agreement_id', $oldAgreementId, $newAgreementId
            ),
        ];

        return $response;
    }

    /**
     * Get status data for changes in the category and subcategory fields of a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @return array An array containing the event and description of the category and subcategory changes.
     */
    private static function getCategoryValChangedStatusData(Contract $contract): array
    {
        $oldSubCategoryId = $contract->getOriginal('category_id');
        $oldSubCategory = empty($oldSubCategoryId) ? null : Category::with(['parentCategory:id,name'])
            ->select(['id', 'name', 'parent_id', 'is_hidden'])
            ->firstWhere('id', $oldSubCategoryId);
        $oldSubCategoryName = $oldSubCategory->name ?? 'empty';

        $newSubCategoryId = $contract->category_id;
        $newSubCategory = empty($newSubCategoryId) ? null : Category::with(['parentCategory:id,name'])
            ->select(['id', 'name', 'parent_id', 'is_hidden'])
            ->firstWhere('id', $newSubCategoryId);
        $newSubCategoryName = $newSubCategory->name ?? 'empty';

        $response = [
            self::getLogAditionalDataObject(
                $contract,
                'Edited "Subategory"',
                'From ' . $oldSubCategoryName . ' -> ' . $newSubCategoryName,
                'category_id', $oldSubCategoryId, $newSubCategoryId
            ),
        ];
        if ($oldSubCategory?->parentCategory?->id !== $newSubCategory?->parentCategory?->id) {
            $oldCategoryName = $oldSubCategory?->parentCategory?->name ?? 'empty';
            $newCategoryName = $newSubCategory?->parentCategory?->name ?? 'empty';
            $response[] = [
                'event' => 'Edited "Category"',
                'description' => 'From ' . $oldCategoryName . ' -> ' . $newCategoryName,
            ];
        }

        return $response;
    }

    /**
     * Get status data for changes in a boolean field of a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @param  string  $fieldName  The name of the boolean field being checked.
     * @param  string  $fieldTitle  The title of the field for display purposes.
     * @return array An array containing the event and description of the boolean field change.
     */
    private static function getBooleanValChangedStatusData(
        Contract $contract,
        string $fieldName,
        string $fieldTitle
    ): array {
        $oldValue = $contract->getOriginal($fieldName);
        $oldLabel = empty($oldValue) || !(bool)$oldValue ? 'Inactive' : 'Active';

        $newValue = (bool)$contract->{$fieldName} ? 'Active' : 'Inactive';
        $newLabel = empty($newValue) ? 'Inactive' : $newValue;

        return [
            self::getLogAditionalDataObject(
                $contract,
                'Edited "' . $fieldTitle . '"',
                'From ' . $oldLabel . ' -> ' . $newLabel,
                $fieldName, $oldValue, $newValue
            ),
        ];
    }

    /**
     * Get status data for changes in a specified field of a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @param  string  $fieldName  The name of the field being checked.
     * @param  string  $fieldTitle  The title of the field for display purposes.
     * @return array An array containing the event and description of the field change.
     */
    private static function getFieldChangedStatusData(
        Contract $contract,
        string $fieldName,
        string $fieldTitle
    ): array {
        $oldValue = $contract->getOriginal($fieldName);
        $oldLabel = empty($oldValue) ? 'empty' : $oldValue;

        $newValue = $contract->{$fieldName};
        $newLabel = empty($newValue) ? 'empty' : $newValue;

        return [
            self::getLogAditionalDataObject(
                $contract,
                'Edited "' . $fieldTitle . '"',
                'From ' . $oldLabel . ' -> ' . $newLabel,
                $fieldName, $oldValue, $newValue
            ),
        ];
    }

    /**
     * Get status data for changes in the billing frequency of a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @return array An array containing the event and description of the billing frequency change.
     */
    private static function getBillingFrequencyChangedStatusData(
        Contract $contract,
    ): array {
        $oldValue = $contract->getOriginal('recurrence');
        $oldLabel = empty($oldValue) ? 'empty' : BillingFrequencyLabelEnum::getValue($oldValue);

        $newValue = $contract->recurrence;
        $newLabel = empty($newValue) ? 'empty' : BillingFrequencyLabelEnum::getValue($newValue);

        return [
            self::getLogAditionalDataObject(
                $contract,
                'Edited "Billed Every"',
                'From ' . $oldLabel . ' -> ' . $newLabel,
                'recurrence', $oldValue, $newValue
            ),
        ];
    }

    /**
     * Get status data for a date field change in a contract.
     *
     * @param  Contract  $contract  The contract being checked.
     * @param  string  $fieldName  The name of the date field.
     * @param  string  $fieldTitle  The title of the date field.
     * @return array An array containing the event and description of the date change.
     */
    private static function getDateChangedStatusData(
        Contract $contract,
        string $fieldName,
        string $fieldTitle
    ): array {
        $oldValue = $contract->getOriginal($fieldName);
        $oldLabel = empty($oldValue) ? 'empty' : Carbon::createFromFormat('Y-m-d H:i:s', $oldValue)->format('M d, Y');

        $newValue = $contract->{$fieldName};
        $newLabel = empty($newValue) ? 'empty' : Carbon::createFromFormat('Y-m-d H:i:s', $newValue)->format('M d, Y');

        return [
            self::getLogAditionalDataObject(
                $contract,
                'Edited "' . $fieldTitle . '"',
                'From ' . $oldLabel . ' -> ' . $newLabel,
                $fieldName, $oldValue, $newValue
            ),
        ];
    }
}
