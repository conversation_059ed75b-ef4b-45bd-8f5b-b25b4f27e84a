<?php

namespace App\Notifications;

use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Enums\MailGunTags;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AuthService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Password;

class EmailRegisteredMSPNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct() {}

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        // Generate a password reset token for a given email
        $token = Password::createToken($notifiable);
        $url = config('app.fe_url') . '/reset-password/?email=' . urlencode($notifiable->email) . '&token=' . $token;
        $emailDetails = AuthService::emailDetails(EmailType::RegisteredEmailMSP);
        $emailBladeFile = EmailBladeFiles::RegisteredEmailMSP;
        $emailCustomData = EmailService::prepareEmailCustomData(
            $emailBladeFile,
            [$notifiable->id]
        );

        return (new CustomMailMessage())->markdown($emailDetails['view'], [
            'notifiable' => $notifiable,
            'headerText' => $emailCustomData->header_text, 'headerImage' => $emailCustomData->header_image,
            'introText' => $emailCustomData->intro_text,
            'footerText' => $emailCustomData->footer_text, 'domain' => $emailDetails['domain'],
            'url' => $url,
        ])->subject($emailCustomData->subject)
            ->from($emailDetails['fromEmail'], $emailDetails['fromDisplayName'])
            ->withSymfonyMessage(function ($message) {
                $headers = $message->getHeaders();
                $headers->addTextHeader("X-Mailgun-Tag", MailGunTags::registeredEmailMSP);
            });
    }
}
