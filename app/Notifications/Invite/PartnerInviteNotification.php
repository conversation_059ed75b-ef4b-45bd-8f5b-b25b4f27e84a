<?php

namespace App\Notifications\Invite;

use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Enums\MailGunTags;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AuthService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PartnerInviteNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(
        protected string $email,
        protected string $url,
        protected ?string $partnerName
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ["mail"];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        $emailDetails = AuthService::emailDetails(EmailType::PartnerInvite);
        $emailCustomData = EmailService::prepareEmailCustomData(
            EmailBladeFiles::PartnerInvite,
            [],
            [
                "partner_name" => $this->partnerName ?? "",
                "email" => $this?->email,
            ]
        );

        return (new CustomMailMessage())->markdown($emailDetails["view"], [
            "notifiable" => $notifiable,
            "headerText" => $emailCustomData->header_text, "headerImage" => $emailCustomData->header_image,
            "introText" => $emailCustomData->intro_text,
            "footerText" => $emailCustomData->footer_text, "domain" => $emailDetails["domain"],
            "route" => $this->url,
        ])->subject($emailCustomData->subject)
            ->from($emailDetails["fromEmail"], $emailDetails["fromDisplayName"])
            ->withSymfonyMessage(function ($message) {
                $headers = $message->getHeaders();
                $headers->addTextHeader("X-Mailgun-Tag", MailGunTags::partnerInvite);
            });
    }
}
