<?php

namespace App\Notifications;

use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Enums\GeneralRedirectUrl;
use App\Enums\MailGunTags;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AuthService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class VendorWeeklySummaryNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public Collection $weeklySummary;

    public string $redirectUrl;

    public string $industryUrl;

    public array $callbacks = [];

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Collection $weeklySummary)
    {
        $this->weeklySummary = $weeklySummary;
        $this->redirectUrl = config('app.fe_url') . GeneralRedirectUrl::myStack;
        $this->industryUrl = config('app.fe_url') . GeneralRedirectUrl::industryEvents;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        Log::debug('SWSTMSP: sending weekly summary to MSP users::' . $notifiable->id . '::' . $notifiable->email);
        $emailDetails = AuthService::emailDetails(EmailType::VendorWeeklySummary);
        $emailCustomData = EmailService::prepareEmailCustomData(
            EmailBladeFiles::VendorWeeklySummary,
            [$notifiable->id],
            [
                'email' => $notifiable?->email,
                'first_name' => $notifiable?->first_name,
                'last_name' => $notifiable?->last_name,
            ]
        );

        return (new CustomMailMessage())
            ->markdown(
                $emailDetails['view'],
                [
                    'notifiable' => $notifiable,
                    'headerText' => $emailCustomData->header_text, 'headerImage' => $emailCustomData->header_image,
                    'introText' => $emailCustomData->intro_text,
                    'footerText' => $emailCustomData->footer_text, 'domain' => $emailDetails['domain'],
                    'redirectUrl' => $this->redirectUrl,
                    'industryUrl' => $this->industryUrl,
                    'weeklySummary' => $this->weeklySummary,
                ]
            )
            ->subject($emailCustomData->subject)
            ->from($emailDetails['fromEmail'], $emailDetails['fromDisplayName'])
            ->withSymfonyMessage(function ($message) {
                $headers = $message->getHeaders();
                $headers->addTextHeader('X-Mailgun-Tag', MailGunTags::prmVendorWeeklySummaryNotification);
            });
    }
}
