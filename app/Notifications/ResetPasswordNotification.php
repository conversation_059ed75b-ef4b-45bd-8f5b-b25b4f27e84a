<?php

namespace App\Notifications;

use App\Enums\Email\EmailBladeFiles;
use App\Enums\Email\EmailType;
use App\Models\Company\CompanyClient;
use App\Notifications\Messages\CustomMailMessage;
use App\Services\AuthService;
use App\Services\Email\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ResetPasswordNotification extends Notification
{
    use Queueable;

    private string $url;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($data, protected string $domain = '')
    {
        $this->url = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @throws \Exception
     */
    public function toMail($notifiable): MailMessage
    {
        $client = CompanyClient::with('client')
            ->where('client_id', $notifiable->company_id)
            ->first();
        if (!$client) {
            $whitelabelingCompanyId = null;
            $bladeFile = EmailBladeFiles::ResetPassword;
            $emailDetails = AuthService::emailDetails(EmailType::ResetPassword, $this->domain);
            $companyName = '';
        } else {
            $whitelabelingCompanyId = $client->client_id;
            $bladeFile = EmailBladeFiles::ResetPasswordClient;
            $emailDetails = AuthService::emailDetails(EmailType::ResetPasswordClient, $this->domain);
            $companyName = $client->client->name;
            $emailDetails['fromDisplayName'] = $client->client->name;
        }
        $emailCustomData = EmailService::prepareEmailCustomData(
            $bladeFile,
            [$notifiable->id],
            [
                'email' => $notifiable?->email,
                'first_name' => $notifiable?->first_name,
                'last_name' => $notifiable?->last_name,
            ],
            $whitelabelingCompanyId,
            $this->domain
        );

        return (new CustomMailMessage())->mailer($emailDetails['mailer'])
            ->markdown($emailDetails['view'], [
                'notifiable' => $notifiable,
                'companyName' => $companyName,
                'headerText' => $emailCustomData->header_text, 'headerImage' => $emailCustomData->header_image,
                'introText' => $emailCustomData->intro_text,
                'footerText' => $emailCustomData->footer_text, 'domain' => $emailDetails['domain'],
                'route' => $this->url,
            ])->subject($emailCustomData->subject)
            ->from($emailDetails['fromEmail'], $emailDetails['fromDisplayName']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
