<?php

namespace App\Enums\ContactList;

use BenSampo\Enum\Enum;

final class ContactListPresets extends Enum
{
    const ALL_CURRENT_PARTNERS = "All Current Partners";
    const ALL_PROSPECTS = "All Prospects";
    const ALL_PAST_PARTNERS = "All Past Partners";
    const ALL_OTHER_PARTNERS = "All partnerships marked as Other";
    const ALL_MSPS = "All MSPs";
    const SUBSCRIBED_MSPS = "Subscribed MSPs";
    const FREE_MSPS = "Free MSPs";
    const ALL_VENDORS = "All Vendors";
    const SEND_TO_ALL = "Send to All";
    const SUBSCRIBED_VENDORS = "Subscribed Vendors";
    const FREE_VENDORS = "Free Vendors";
    const CUSTOM = "Custom List";
    const TEMPORARY = "Temporary List";
}
