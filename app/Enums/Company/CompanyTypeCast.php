<?php

namespace App\Enums\Company;

use BenSampo\Enum\Enum;

final class CompanyTypeCast extends Enum
{
    const COMPANY_TYPE_CAST_ARRAY = [
        CompanyType::MSP_CLIENT => 'Customer',
        CompanyType::ISP_ALL => 'Company User',
        CompanyType::MSP_LOCATION => 'Location',
        CompanyType::FranchiseMsp => 'Affiliate',
        CompanyType::CHANNEL_PROGRAM => 'CHANNEL_PROGRAM',
        CompanyType::MSP => 'MSP',
        CompanyType::FRANCHISE_CORPORATE_MSP => 'FRANCHISE_CORPORATE_MSP',
        CompanyType::MSSP => 'MSSP',
        CompanyType::VAR => 'VAR',
        CompanyType::DIRECT => 'DIRECT',
        CompanyType::ITConsultant => 'IT Consultant',
        CompanyType::Vendor => 'Vendor',
        CompanyType::VENDOR_ALL => 'VENDOR_ALL',
        CompanyType::Distributor => 'Distributor',
        CompanyType::SystemsIntegrator => 'Systems Integrator',
        CompanyType::ISP => 'ISP',
        CompanyType::Media => 'Media',
        CompanyType::PrivateEquityOrInvestor => 'Private Equity or Investor',
        CompanyType::Other => 'Other',
        CompanyType::Individual => 'INDIVIDUAL',
        CompanyType::MasterAgent => 'MASTER AGENT',
        CompanyType::ConferenceAndEvents => 'CONFERENCE EVENTS',
        CompanyType::MediaGroupOrPersonality => 'Media',
        CompanyType::PeerGroup => 'PEER_GROUP',
        CompanyType::FinanceOrTechInvestor => 'FINANCE_ALL',
    ];
}
