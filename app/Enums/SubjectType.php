<?php

namespace App\Enums;

use App\Models\Advertisement\Advertisement;
use App\Models\Company\Company;
use App\Models\IndustryEvent\IndustryEvent;
use App\Models\PitchEvent\PitchEvent;
use App\Models\User;
use BenSampo\Enum\Enum;

final class SubjectType extends Enum
{
    const userProfile = User::class;

    const companyProfile = Company::class;

    const industryEvent = IndustryEvent::class;

    const pitchEvent = PitchEvent::class;

    const advertisement = Advertisement::class;
}
