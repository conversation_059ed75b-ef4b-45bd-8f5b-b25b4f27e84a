<?php

namespace App\Enums\Deals;

use App\Enums\Filters\FilterTypesEnum;
use BenSampo\Enum\Enum;

final class DealFiltersEnum extends Enum
{
    public const submitters = [
        "multiple" => true,
        "placeholder" => "Requested By",
        "is_public" => true,
    ];
    public const companies = [
        "multiple" => true,
        "placeholder" => "Company",
        "is_public" => true,
    ];
    public const statuses = [
        "multiple" => true,
        "placeholder" => "Status",
        "is_public" => true,
    ];
    public const submited_at = [
        "type" => FilterTypesEnum::DATE_RANGE,
        "multiple" => false,
        "placeholder" => "Date",
        "is_public" => true,
    ];
}
