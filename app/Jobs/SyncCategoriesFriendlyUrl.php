<?php

namespace App\Jobs;

use App\Models\Category\Category;
use App\Services\CategoryService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncCategoriesFriendlyUrl implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     *
     * @throws \Exception
     */
    public function handle(): void
    {
        $categories = Category::with('parentCategory')->get();
        $parentCategories = $categories->where('parent_id', null);
        $childrenCategories = $categories->where('parent_id', '!=', null);

        $parentCategories->each(function ($category) {
            $this->setCategoryFriendlyUrl($category);
        });

        $childrenCategories->each(function ($category) {
            $this->setCategoryFriendlyUrl($category);
        });
    }

    private function setCategoryFriendlyUrl($category)
    {
        try {
            $friendlyUrl = CategoryService::generateFriendlyUrl($category);
            $category->friendly_url = $friendlyUrl;
            $category->save();
        } catch (\Throwable $ex) {
            Log::error('Could not save category friendly URL: ' . $ex->getMessage());
        }
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping('' . now()->valueOf())];
    }
}
