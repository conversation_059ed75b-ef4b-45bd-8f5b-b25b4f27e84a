<?php

namespace App\Jobs;

use App\Enums\AnalyticAction;
use App\Enums\FileUploadSummaryStatus;
use App\Enums\FolderContentStatus;
use App\Enums\FolderContentType;
use App\Enums\Partner\PartnerPageSectionContentSubjectType;
use App\Models\Company\Company;
use App\Models\Folder\CompanyFolder;
use App\Models\MediaGallery;
use App\Models\Partner\PartnerPageSectionContent;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\FileUpload\FileUploadService;
use App\Services\FolderService;
use App\Services\MediaService;
use App\Services\Partner\PartnerPageService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use TypeError;

class SaveMediaToFinalDisk implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public array $finalProperties;
    public string $mediaCollection;
    public Model $subject;
    public Model $authorModel;
    public Authenticatable|User $loggedInUser;
    public array $customProperties;
    public array $notifyUsers;
    public ?bool $notifyOwner = true;
    public ?CompanyFolder $folder = null;
    public ?string $summarySessionId = null;
    public ?string $summaryContext = null;
    public ?string $summaryFileId = null;
    public ?string $disk;

    /**
     * Create a new job instance.
     *
     * @param  array  $finalProperties  An array with additional properties regarding the thumbnail and Partner Content Data.
     * @param  Model  $subject  The subject model to which the media belongs.
     * @param  Model  $authorModel  The author model associated with the media.
     * @param  string  $mediaCollection  The media collection name to which the media will be added.
     * @param  Authenticatable|User  $loggedInUser  The logged-in user who initiated the upload.
     * @param  array  $customProperties  Custom properties for the media.
     * @param  array|null  $notifyUsers  List of users to be notified upon media processing completion.
     * @param  bool|null  $notifyOwner  Whether to notify the owner of the media upon completion (default = true).
     * @param  CompanyFolder|null  $folder  The Company Folder in which the media will be stored.
     * @param  string|null  $summarySessionId  The session ID for the file upload summary.
     * @param  string|null  $summaryContext  The context for the file upload summary.
     * @param  string|null  $summaryFileId  The file ID for the file upload summary.
     * @param  string|null  $disk  The disk on which the media will be stored, defaulting to "backBlazePublic".
     * @return void
     */
    public function __construct(
        array $finalProperties,
        Model $subject,
        Model $authorModel,
        string $mediaCollection,
        Authenticatable|User $loggedInUser,
        array $customProperties = [],
        ?array $notifyUsers = [],
        ?bool $notifyOwner = true,
        ?CompanyFolder $folder = null,
        ?string $summarySessionId = null,
        ?string $summaryContext = null,
        ?string $summaryFileId = null,
        ?string $disk = "backBlazePublic",
    ) {
        $this->finalProperties = $finalProperties;
        $this->subject = $subject;
        $this->authorModel = $authorModel;
        $this->mediaCollection = $mediaCollection;
        $this->customProperties = $customProperties;
        $this->loggedInUser = $loggedInUser;
        $this->notifyUsers = $notifyUsers;
        $this->notifyOwner = $notifyOwner;
        $this->folder = $folder;
        $this->summarySessionId = $summarySessionId;
        $this->summaryContext = $summaryContext;
        $this->summaryFileId = $summaryFileId;
        $this->disk = $disk;
    }

    /**
     * Execute the job.
     *
     * @throws Exception
     */
    public function handle(): void
    {
        $source = __CLASS__ . '::' . __FUNCTION__;
        Log::debug($source . '::Starting Job');
        $mediaPath = $this->customProperties['path'] . $this->customProperties['name'];
        $media = $this->subject->addMediaFromDisk($mediaPath, 'shared')
            ->withCustomProperties($this->customProperties)
            ->toMediaCollection($this->mediaCollection);
        // Adding media to folder
        if ($media && $this->folder) {
            FolderService::addContentToFolder(
                $this->folder,
                $media->id,
                FolderContentType::getKey(FolderContentType::video),
                FolderContentStatus::visible
            );
        }
        // Saving thumbnail
        if ($this->finalProperties['hasThumbnail']) {
            $customProperties['media_id'] = '' . $media->id;
            $thumbnailMediaCreated = $this->subject
                ->addMediaFromDisk($this->finalProperties['thumbnailPath'], 'shared')
                ->withCustomProperties($customProperties)
                ->addCustomHeaders(['ACL' => 'public-read'])
                ->toMediaCollection(config('custom.media_collections.video_thumbnails'), $this->disk);
            $media->setCustomProperty('thumbnail_id', '' . $thumbnailMediaCreated->id);
            $media->save();
        }
        if (isset($this->finalProperties['partner_content_data'])) {
            PartnerPageSectionContent::create(
                [
                    'subject_id' => $this->finalProperties['partner_content_data']['subject_id'] ?? $media->id,
                    'subject_type' => $this->finalProperties['partner_content_data']['subject_type'] ?? PartnerPageSectionContentSubjectType::media,
                    'author_id' => $media->getCustomProperty('author_id'),
                    'partner_page_section_id' => $this->finalProperties['partner_content_data']['partner_page_section_id'],
                    'order' => 0,
                ]
            );
        } elseif ($this->authorModel instanceof Company) {
            // Adding vendor public content to PRM
            if ($this->subject instanceof MediaGallery) {
                PartnerPageService::addVendorContentToPRM($this->authorModel, $this->subject);
            } else {
                $isPartnerContent = Arr::get($media->custom_properties, 'is_partner_content');
                if ($isPartnerContent === null) {
                    try {
                        $prmMedia = MediaService::duplicateMedia($this->authorModel, $media, $this->loggedInUser->id,
                            $this->loggedInUser->complete_name, $this->mediaCollection, true);
                        PartnerPageService::addVendorContentToPRM($this->authorModel, $prmMedia);
                    } catch (\Exception $e) {
                        Log::error(__CLASS__ . '::' . __FUNCTION__ . '::' . $e->getMessage());
                    }
                } else {
                    PartnerPageService::addVendorContentToPRM($this->authorModel, $media);
                }
            }
        }
        // Updating upload summary
        if ($this->summarySessionId && $this->summaryContext && $this->summaryFileId) {
            FileUploadService::updateSummaryFileStatus(
                $this->summarySessionId,
                $this->summaryContext,
                $this->summaryFileId,
                FileUploadSummaryStatus::uploaded
            );
        }
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Media uploaded to backBlaze successfully.');
        // Notifying Logged In User
        if ($this->notifyOwner) {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Sending notification to owner. ID: ' . $this->loggedInUser->id);
            $action = $this->mediaCollection === config('custom.media_collections.contract_document')
                ? AnalyticAction::distributorUploadComplete
                : AnalyticAction::videoProcessed;
            dispatch(new StoreUsersNotifications(
                $this->loggedInUser->id,
                $media->id,
                Media::class,
                $action,
                $media->custom_properties,
                $this->notifyUsers
            ));
        }
        //Save data to activity log table
        $activityLogsData = $this->finalProperties['activity_logs_data'] ?? null;
        if (!is_null($activityLogsData) && isset($activityLogsData['action'], $activityLogsData['author_model'],
            $activityLogsData['author_id'], $activityLogsData['subject_model'], $activityLogsData['subject_id'],
            $activityLogsData['additional_data'])
        ) {
            Log::debug($source . 'Storing Activity Log: ' . json_encode($activityLogsData));
            ActivityLogsService::store(
                $activityLogsData['action'],
                $activityLogsData['author_model'],
                $activityLogsData['author_id'],
                $activityLogsData['subject_model'],
                $activityLogsData['subject_id'],
                $activityLogsData['additional_data'],
                $activityLogsData['ip_address'] ?? null,
            );
        }
        /*
         * *** AVOIDING MEMORY LEAKS AND DISK CONSUMPTION ***
         * When executing queue worker with 'php artisan queue:work'
         * the files were not deleted from filesystem after being uploaded to backBlaze
         * until the queue worker was restarted.
         * The following line fixes that behaviour.
        */
        app('queue.worker')->shouldQuit = 1;
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Finishing Job');
    }

    public function failed(Exception|TypeError $err)
    {
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Job failed with message: ' . $err->getMessage());
        // Updating upload summary
        if ($this->summarySessionId && $this->summaryContext && $this->summaryFileId) {
            FileUploadService::updateSummaryFileStatus(
                $this->summarySessionId,
                $this->summaryContext,
                $this->summaryFileId,
                FileUploadSummaryStatus::error
            );
        }
    }

    public function middleware(): array
    {
        $jobID = (!is_null($this->summarySessionId) && !is_null($this->summaryContext))
            ? $this->summarySessionId . '_' . $this->summaryContext
            : $this->loggedInUser->id;

        return [new WithoutOverlapping($jobID)];
    }
}
