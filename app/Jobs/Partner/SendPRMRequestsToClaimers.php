<?php

namespace App\Jobs\Partner;

use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Enums\UserStatus;
use App\Models\MSPFollowingPartner;
use App\Notifications\Partner\PartnerPortalInvitationRequestNotification;
use App\Services\FeatureFlagService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendPRMRequestsToClaimers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::debug('SendPRMRequestsToClaimers - start of job');
        $featureFlag = FeatureFlagService::findByName('SEND_VENDOR_PENDING_REQUEST_NOTIFICATION');
        if ($featureFlag->activated) {
            $pendingGroupedRequests = $this->getLatestPendingPRMRequests()?->groupBy('followed_partner_id');
            foreach ($pendingGroupedRequests as $pendingRequests) {
                $this->sendRequestedNotification($pendingRequests);
            }
        }
        Log::debug('SendPRMRequestsToClaimers - end of job');
    }

    private function getLatestPendingPRMRequests()
    {
        return MSPFollowingPartner::with(
            [
                'partner' => function ($query) {
                    $query->select('companies.id', 'companies.name', 'subdomain', 'friendly_url')
                        ->where('partner_flag', true);
                },
                'followerPartner' => function ($query) {
                    $query->select('companies.id', 'companies.name', 'subdomain', 'friendly_url');
                },
                'partner.claimers' => function ($query) {
                    $query->select('users.id', 'first_name', 'last_name', 'handle', 'email')
                        ->where('status', UserStatus::Active);
                },
                'invitedBy' => function ($query) {
                    $query->select('users.id', 'first_name', 'last_name', 'handle', 'email')
                        ->where('status', UserStatus::Active);
                },
            ]
        )
            ->where('status', PartnerPortalInvitationStatus::Requested)
            // ->whereRaw(
            //     "not exists
            //     (
            //         select 1 from my_stack where
            //             my_stack.company_id = company_partners.follower_partner_id and
            //             my_stack.stack_company_id = company_partners.followed_partner_id
            //     )"
            // )
            ->where('created_at', '>=', now()->subDays(1))
            ->whereNull('deleted_at')
            ->get();
    }

    private function sendRequestedNotification($pendingRequests)
    {
        Log::debug("SendPRMRequestsToClaimers - Sending notification to partner: {$pendingRequests->first()->partner?->name}");
        if ($pendingRequests->first()->partner?->claimers->isNotEmpty()) {
            $claimers = $pendingRequests->first()->partner?->claimers;
            Log::debug('SendPRMRequestsToClaimers - Claimers: ' . $claimers->pluck('email')->implode(', '));
            $redirectUrl = config('app.fe_url') . config('common.chat.vendor_url') . $pendingRequests->first()->partner?->friendly_url . '?requests=1';
            $vendorName = $pendingRequests->first()->partner?->name;
            foreach ($claimers as $claimer) {
                Log::debug("SendPRMRequestsToClaimers - Sending notification to claimer: {$claimer->email}");
                $claimer->notify(new PartnerPortalInvitationRequestNotification($pendingRequests, $redirectUrl, $vendorName));
            }
        }
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping('' . now()->valueOf())];
    }
}
