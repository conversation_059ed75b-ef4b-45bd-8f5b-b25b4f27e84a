<?php

namespace App\Jobs;

use App\Models\IndustryEvent\IndustryEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RemoveFeatureFlagForPassedEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::debug('RemoveFeatureFlagForPassedEvent->handle: start');
            IndustryEvent::where('feature_flag', true)
                ->where('feature_end_date', '<', now())
                ->update([
                    'feature_flag' => false,
                ]);
            Log::debug('RemoveFeatureFlagForPassedEvent->handle: end');
        } catch (\Exception $ex) {
            Log::error('ERROR:' . __CLASS__ . '::' . __FUNCTION__ . ': ' . $ex->getMessage());
        }
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping('' . now()->valueOf())];
    }
}
