<?php

namespace App\Jobs\SEO;

use App\Models\AppConfiguration;
use App\Services\UtilityService;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class BuilderSitemaps implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $location = __CLASS__ . '::' . __FUNCTION__;
        Log::debug('BuilderSitemaps::' . $location . '::Job started');

        $appConfigKeyName = 'BUILDER_SITEMAP_PAGE_MODELS';

        // get page models to create sitemap for
        $pageModelsQuery = AppConfiguration::select('value')->where('key', $appConfigKeyName)->first();

        if (empty($pageModelsQuery)) {
            Log::error('BuilderSitemaps::' . $appConfigKeyName . ' app config is not present in the app_configurations table. Cannot run the BuilderSitemaps job.');
        }

        $pageModels = trim($pageModelsQuery->value);

        if ($pageModels === '') {
            Log::warn('BuilderSitemaps::' . $appConfigKeyName . ' app config is present in the app_configurations table, but has no value. Cannot run the BuilderSitemaps job.');
        }

        // split the string by comma into an array so we can loop thru it
        $pageModelArray = explode(',', $pageModels);

        $builderPublicKey = config('builder.API_PUBLIC_KEY');
        $builderSEOApiUrl = config('builder.SEO_API_URL');
        $builderSEOApiUrl = str_replace('{api_public_key}', $builderPublicKey, $builderSEOApiUrl);

        $builderClient = new Client([
            'headers' => ['Accept' => 'application/json'],
        ]
        );

        foreach ($pageModelArray as $pageModel) {
            $currentPageModel = trim($pageModel);
            Log::debug('BuilderSitemaps:: Current page model is: ' . $currentPageModel);

            $builderSEOApiUrlForThisSection = str_replace('{page_model}', $currentPageModel, $builderSEOApiUrl);

            try {
                // call builder api to get json
                $response = $builderClient->request('GET', $builderSEOApiUrlForThisSection);

                $json = json_decode($response->getBody()->getContents());

                // create the xml
                $xml = '<?xml version="1.0" encoding="UTF-8"?>';
                $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';

                foreach ($json->results as $data) {
                    if ($data->published === 'published') {
                        // last updated from builder comes back as milliseconds, convert to a date.
                        $mil = $data->lastUpdated;
                        $seconds = ceil($mil / 1000);
                        $lastUpdateDate = date("Y-m-d", $seconds);

                        Log::debug('BuilderSitemaps:: Current url being saved is: ' . $data->data->url);
                        $xml .= '<url>';
                        $xml .= '<loc>' . config('app.fe_url') . $data->data->url . '</loc>';
                        $xml .= '<lastmod>' . $lastUpdateDate . '</lastmod>';
                        $xml .= '<priority>1</priority>';
                        $xml .= '<changefreq>weekly</changefreq>';
                        $xml .= '</url>';
                    } else {
                        Log::debug('BuilderSitemaps:: Skipping url because it is not published: ' . $data->data->url);
                    }
                }

                $xml .= '</urlset>';

                $filename = 'sitemap/' . $currentPageModel . '.xml';

                UtilityService::saveToStorageByDisk('backBlazeSitemap', $filename, $xml);
            } catch (\Exception $ex) {
                // log the error but move on with the next page model
                Log::error('BuilderSitemaps:: Could not get a response from the page model: ' . $currentPageModel . '. ' . $ex->getMessage());
            }
        }

        Log::debug('BuilderSitemaps::' . $location . '::Job finished');
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping('' . now()->valueOf())];
    }
}
