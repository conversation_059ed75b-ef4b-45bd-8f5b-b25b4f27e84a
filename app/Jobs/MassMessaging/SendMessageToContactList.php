<?php

namespace App\Jobs\MassMessaging;

use App\Enums\AnalyticAction;
use App\Enums\Chat\ChatMessageReadStatus;
use App\Enums\Chat\ChatMessageType;
use App\Enums\ContactList\ContactListTypes;
use App\Jobs\StoreUsersNotifications;
use App\Models\Chat\ChatMessage;
use App\Models\Company\Company;
use App\Models\User;
use App\Services\AppConfig;
use App\Services\Chat\ChatService;
use App\Services\Company\CompanyContactListService;
use App\Services\FeatureFlagService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * Job to send a mass message to a contact list.
 *
 * This job processes the sending of a mass message to all contacts in a specified contact list.
 * It handles both permanent and temporary contact lists, logs errors if any occur, and sends
 * notifications to the sender about the status of the message sending process.
 *
 * @param  User  $loggedUser  The user sending the message.
 * @param  int  $creatorCompanyId  The ID of the company creating the message.
 * @param  string  $contactListType  The type of the contact list from ContactListTypes enum.
 * @param  string  $contactListId  The ID of the contact list (company_contacts_lists.id OR keyof ContactListPresets
 *                                 enum OR "TEMPORARY").
 * @param  array  $selectedPartners  The selected partners IDs for a TEMPORARY contact list.
 * @param  string  $message  The message to be sent.
 * @param  string  $redirectUrl  The URL to redirect whe the receiver clicks on the message.
 */
class SendMessageToContactList implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public User $loggedUser,
        public string $creatorCompanyId,
        public string $contactListType,
        public string $contactListId,
        public array $selectedPartners,
        public string $message,
        public string $redirectUrl,
        public string $messageTypeOption,
        public ?string $sendingId = null,
        public int $page = 1,
        public array $withErrors = [],
        public array $companyIds = []
    ) {
        $this->sendingId = is_null($sendingId) ? uniqid('MESSAGE_SENDING_') : $sendingId;
    }

    /**
     * Execute the job.
     *
     * @throws ValidationException|\BenSampo\Enum\Exceptions\InvalidEnumMemberException
     */
    public function handle(): void
    {
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::SENDING MESSAGE', [
            'SENDING_ID' => $this->sendingId,
            'PAGE' => $this->page,
            'MESSAGE' => $this->message,
        ]);
        $creatorCompany = Company::findOrFail($this->creatorCompanyId);
        $customProperties = [
            'sendingId' => $this->sendingId,
            'contactListType' => $this->contactListType,
            'contactListId' => $this->contactListId,
            'contactListName' => $this->contactListId,
            'message' => $this->message,
        ];
        if ($this->contactListId === ContactListTypes::TEMPORARY) {
            $companies = CompanyContactListService::getContactListByCompanyPartnerIDs($creatorCompany,
                $this->selectedPartners);
        } else {
            $contactList = CompanyContactListService::getContactListByID($creatorCompany, $this->contactListId);
            $customProperties['contactListName'] = $contactList->name;
            $companies = CompanyContactListService::getContactListItemsByID($creatorCompany, $this->contactListId);
        }
        $companiesPerPage = AppConfig::loadAppConfigByKey('MASS_MESSAGING_COMPANIES_PER_PAGE', 100)
            ->value;
        $pageData = $companies->paginate($companiesPerPage, ['*'], 'page', $this->page);
        $lastPage = $pageData->lastPage();
        $companies = $pageData->getCollection();
        $messages_count = 0;
        $chatEmailNotificationFrequencyInMinutes =
            AppConfig::loadAppConfigByKey('CHAT_EMAIL_NOTIFICATION_FREQUENCY_IN_MINUTES', 2)->value;
        $sendChatNotifications = FeatureFlagService::findByName('SEND_CHAT_NOTIFICATIONS')->activated;
        foreach ($companies as $company) {
            if (!(in_array($company->id, $this->companyIds))) {
                Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::LOADING CONTACTS FOR COMPANY: ' . $company->id);
                $contacts = CompanyContactListService::getContactsFromCompanies([$company->id])->get();
                foreach ($contacts as $contact) {
                    Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::CONTACT ID: ' . $contact->id);
                    DB::beginTransaction();

                    try {
                        $createdMessage = ChatMessage::create([
                            'message_type' => ChatMessageType::massMessageChat, 'message' => $this->message,
                            'date' => Carbon::now(), 'creator' => $this->loggedUser->id, 'receiver' => $contact->id,
                            'pusher_channel' => config('custom.pusher_general_channel'), 'can_ban' => false,
                            'creator_company_id' => $creatorCompany->id,
                            'receiver_company_id' => $company->id,
                            'notification_email_sent' => false,
                            'message_type_option' => $this->messageTypeOption,
                            'read_status' => ChatMessageReadStatus::unread,
                        ]);
                        $createdMessage->load('messageTypeOption');
                        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::CREATED MESSAGE::'
                            . json_encode($createdMessage, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT));
                        DB::commit();
                        ChatService::processNotification(
                            $this->loggedUser,
                            $creatorCompany,
                            $contact,
                            $company,
                            $createdMessage,
                            $this->redirectUrl ?? '',
                            $chatEmailNotificationFrequencyInMinutes,
                            $sendChatNotifications,
                            false
                        );
                        $messages_count++;
                    } catch (Exception $error) {
                        DB::rollBack();
                        Log::error(__CLASS__ . '::' . __FUNCTION__ . '::ERROR::' . $error->getMessage());
                        $this->withErrors[] = [
                            'company_id' => $company->id,
                            'id' => $contact->id,
                            'name' => $contact->first_name . ' ' . $contact->last_name,
                            'error' => $error->getMessage(),
                        ];
                    }
                }
            }
            array_push($this->companyIds, $company->id);
        }
        if ($this->page < $lastPage) {
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::DISPATCHING THE JOB FOR PAGE ' . ($this->page + 1));
            SendMessageToContactList::dispatch(
                $this->loggedUser,
                $this->creatorCompanyId,
                $this->contactListType,
                $this->contactListId,
                $this->selectedPartners ?? [],
                $this->message,
                $this->redirectUrl ?? '',
                $this->messageTypeOption ?? '',
                $this->sendingId,
                $this->page + 1,
                $this->withErrors,
                $this->companyIds
            );

            return;
        }

        $errorsCount = count($this->withErrors);
        if ($errorsCount > 0) {
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::Error sending message to contacts');
            foreach ($this->withErrors as $contacts) {
                Log::error(__CLASS__ . '::' . __FUNCTION__ . json_encode($contacts));
            }
            if ($messages_count === 0) {
                $customProperties['message'] = vsprintf(config('genericMessages.error.MESSAGE_NOT_SENT'),
                    [$customProperties['contactListName']]);
            } else {
                $customProperties['message'] = vsprintf(config('genericMessages.error.MESSAGE_SENT_WITH_ERRORS'), [
                    $customProperties['contactListName'],
                    $errorsCount,
                    ($errorsCount > 1 ? 'contacts' : 'contact'),
                ]);
            }
        } else {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::All messages was sent successfully.');
            $customProperties['message'] = vsprintf(config('genericMessages.success.MESSAGE_SENT_SUCCESSFULLY'),
                [$customProperties['contactListName']]);
        }
        // Sending notification to sender
        dispatch(new StoreUsersNotifications(
            $this->loggedUser->id,
            $this->loggedUser->id,
            User::class,
            AnalyticAction::massMessagingStatus,
            $customProperties
        ));
    }

    public function middleware(): array
    {
        return [new WithoutOverlapping($this->sendingId)];
    }
}
