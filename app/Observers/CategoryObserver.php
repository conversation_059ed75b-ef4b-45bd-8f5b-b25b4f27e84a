<?php

namespace App\Observers;

use App\Services\CategoryService;
use Illuminate\Support\Facades\Log;

class CategoryObserver
{
    public function creating($model)
    {
        $this->assignFriendlyUrl($model);
    }

    private function assignFriendlyUrl($model)
    {
        try {
            $model->friendly_url = CategoryService::generateFriendlyUrl($model);
        } catch (\Throwable $ex) {
            Log::error('Could not update friendly url for category: ' . $ex->getMessage());
        }
    }

    public function updating($model)
    {
        if ($model->isDirty('name')) {
            $this->assignFriendlyUrl($model);
        }
    }
}
