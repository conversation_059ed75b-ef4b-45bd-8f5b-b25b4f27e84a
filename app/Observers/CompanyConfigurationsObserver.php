<?php

namespace App\Observers;

use App\Jobs\Contract\UpdateContractPastPaymentsExchangeRateJob;
use App\Jobs\Contract\UpdateFixedRateContractExchangeRateJob;
use App\Models\Company\CompanyConfigurations;

class CompanyConfigurationsObserver
{
    /**
     * Handle the updated event.
     */
    public function updated(CompanyConfigurations $config): void
    {
        $config->load("company");
        $oldValues = (object)$config->getOriginal();
        $newValues = (object)$config->getAttributes();
        if ($config->isDirty("currency_id") && isset($oldValues->currency_id)) {
            UpdateFixedRateContractExchangeRateJob::dispatch($config->company, $oldValues->currency_id, $newValues->currency_id);
            UpdateContractPastPaymentsExchangeRateJob::dispatch($config->company, $oldValues->currency_id, $newValues->currency_id);
        }
    }
}
