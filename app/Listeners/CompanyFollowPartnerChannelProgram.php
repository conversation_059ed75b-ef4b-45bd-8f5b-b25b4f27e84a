<?php

namespace App\Listeners;

use App\Enums\Company\CompanyType;

class CompanyFollowPartnerChannelProgram
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        $company = $event->company->load('enumType');
        if (!empty($company->enumType)) {
            if ($company->enumType->value !== CompanyType::MSP_CLIENT) {
                $event->company->followChannelProgram();
            }
        }
    }
}
