<?php

namespace App\Http\Controllers\Auth;

use App\Enums\Company\CompanyInviteTypeEnum;
use App\Enums\Company\CompanyType as CompanyTypeEnum;
use App\Enums\UserStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Register\RegisterMSPRequest;
use App\Http\Resources\UserResource;
use App\Log;
use App\Models\Company\Company;
use App\Models\Company\CompanyInvite;
use App\Models\User;
use App\Services\Company\CompanyClientService;
use App\Services\Permission\RoleUserService;
use App\Services\RegisterService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use PHPUnit\Exception;

class RegisterMSPController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/register/msp",
     *     operationId="register/msp",
     *     tags={"RegisterMSPController"},
     *     summary="Register an MSP user",
     *     description="Handle an incoming MSP registration request",
     *
     *     @OA\Response(
     *         response=200,
     *         description="OK"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/register/msp
    // BODY
    // {
    //	'email': 'required|string|max:100',
    //  'first_name': 'required|string|max:100',
    //  'last_name': 'required|email:dns',
    //  'company_name': 'required|profanity',
    //  'signature': 'sometimes|required',
    // }
    // no Bearer Token
    public function registerMSP(RegisterMSPRequest $request): UserResource
    {
        return $this->registerCompany($request, '/mspregister');
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/register/direct",
     *     operationId="register/direct",
     *     tags={"RegisterMSPController"},
     *     summary="Register a Direct user",
     *     description="Handle an incoming Direct registration request",
     *
     *     @OA\Response(
     *         response=200,
     *         description="OK"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/register/direct
    // BODY
    // {
    //	'email': 'required|string|max:100',
    //  'first_name': 'required|string|max:100',
    //  'last_name': 'required|email:dns',
    //  'company_name': 'required|profanity',
    //  'signature': 'sometimes|required',
    // }
    // no Bearer Token
    public function registerDirect(RegisterMSPRequest $request): UserResource
    {
        return $this->registerCompany($request, '/direct-register', CompanyTypeEnum::DIRECT, true);
    }

    /**
     * Lógica extraída de registerMSP para reutilização.
     */
    public function registerCompany(
        RegisterMSPRequest $request,
        string $source,
        string $companyType = CompanyTypeEnum::ISP_ALL,
        bool $useBetterTrackerConfig = false
    ): UserResource {
        try {
            DB::beginTransaction();
            $emailVerifyResults = RegisterService::processEmailValidation($request->email, true);
            $sendConfirmationEmail = true;
            $registrationUrl = $source;
            $email = $request->email;
            $isMspClient = $request->has('signature');
            if ($isMspClient) {
                $sendConfirmationEmail = false;
                $signatureParts = CompanyClientService::decodeInviteSignature($request->signature);
                if ($signatureParts->invite_type === CompanyInviteTypeEnum::individual) {
                    $email = $signatureParts->email;
                }
                $registrationUrl = '/msp-client-register';
            }
            // CREATE THE NEW MSP USER
            $user = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'status' => UserStatus::Active,
                'is_private' => true,
                'email' => $email,
                'email_verified_at' => now(),
                'password' => Hash::make($request->password),
                'registration_url' => $registrationUrl,
                'verification_code_verified' => true,
                'verification_mode' => 'EMAIL',
                'email_verify_results' => $emailVerifyResults,
                'job_title_id' => $request->job_title_id ?? null,
            ]);
            if ($isMspClient) {
                // Methods for MSP Clients
                if ($signatureParts->invite_type === CompanyInviteTypeEnum::open) {
                    // Creating a new MSP CLient
                    $parentCompany = Company::findOrFail($signatureParts->parent_company_id);
                    $client = CompanyClientService::createCompanyClient(
                        $parentCompany,
                        $request->company_name
                    );
                    $childCompanyId = $client->id;
                } else {
                    $childCompanyId = $signatureParts->child_company_id;
                }
                $companyInvite = CompanyInvite::where('parent_company_id', $signatureParts->parent_company_id)
                    ->where('child_company_id', $childCompanyId)
                    ->where('email', $email)
                    ->first();
                if (!$companyInvite) {
                    CompanyInvite::create([
                        'parent_company_id' => $signatureParts->parent_company_id,
                        'child_company_id' => $childCompanyId,
                        'author_id' => $user->id,
                        'email' => strtolower($email),
                        'type' => CompanyTypeEnum::MSP_CLIENT,
                        'activated' => true,
                        'activated_at' => now(),
                    ]);
                } else {
                    $companyInvite->activated = true;
                    $companyInvite->activated_at = now();
                    $companyInvite->save();
                    RoleUserService::updateUserRoleByCompany($companyInvite->childCompany, $companyInvite->role_id, $user);
                }
                $user->company_id = $childCompanyId;
            } else {
                // Methods for regular MSPs
                $result = RegisterService::registerUserCompany($user, true, null, $request->company_name, $companyType);
                if (get_class($result) === AnonymousResourceCollection::class) {
                    $result = RegisterService::registerUserCompany($user, false, null, $request->company_name, $companyType);
                }
                $user->company_id = $result->id;
            }

            $user->save();
            $user = RegisterService::finishUserRegistration($user, true, $sendConfirmationEmail, $useBetterTrackerConfig);
            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            Log::error('ERROR::' . __CLASS__ . '::' . __FUNCTION__
                . '::' . $ex->getMessage() . '::' . $ex->getTraceAsString());

            throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::' . $ex->getMessage(),
            ]);
        }

        return new UserResource($user);
    }
}
