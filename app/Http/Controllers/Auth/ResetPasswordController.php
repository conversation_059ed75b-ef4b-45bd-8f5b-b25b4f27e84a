<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\ResetPasswordRequest;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\View\View;

class ResetPasswordController extends Controller
{
    /**
     * Display the password reset view.
     */
    public function create(Request $request): View
    {
        return view('auth.reset-password', ['request' => $request]);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/reset-password",
     *     operationId="resetPassword",
     *     tags={"ResetPasswordController"},
     *     summary="Reset password",
     *     description="Handle an incoming reset password request.",
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/ResetPasswordRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="OK"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/reset-password
    // BODY
    //{
    //  'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9',
    //	'email': '<EMAIL>',
    //  'password': 'P@ssw0rd!',
    //  'password_confirmation': 'P@ssw0rd!',
    //}
    // no Bearer Token
    public function resetPassword(ResetPasswordRequest $request)
    {
        // Here we will attempt to reset the user's password. If it is successful we
        // will update the password on an actual user model and persist it to the
        // database. Otherwise we will parse the error and return the response.
        $status = Password::broker()->reset([
            'token' => $request->token,
            'email' => $request->email,
            'password' => $request->password,
        ],
            function ($user) use ($request) {
                $user->forceFill([
                    'password' => Hash::make($request->password),
                    'remember_token' => Str::random(60),
                ])->save();
                event(new PasswordReset($user));
            }
        );

        return $status == Password::PASSWORD_RESET
            ? response('', 200)
            : response()->json([
                'message' => __($status),
            ], 400);
    }
}
