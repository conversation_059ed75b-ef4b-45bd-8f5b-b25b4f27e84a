<?php

namespace App\Http\Controllers\Api\Partner;

use App\Helpers\MediaHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Http\Requests\Partner\PartnerAssetStoreRequest;
use App\Http\Resources\Partner\PartnerAssetResource;
use App\Models\Company\Company;
use App\Models\Partner\PartnerPage;
use App\Models\PartnerAsset;
use App\Services\AuthService;
use App\Services\Partner\PartnerAssetService;
use Spatie\MediaLibrary\MediaCollections\Models\Media as PartnerMedia;

class PartnerAssetsController extends Controller
{
    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/partner/page/{PAGE_ID}/asset/store",
     *     operationId="partner/page/assets/store",
     *     tags={"PartneAssetsController"},
     *     summary="Create a new partner Assets",
     *     description="Create a new partner Assets",
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/partner/page/{PAGE_ID}/asset/store
    // Bearer token needed
    // Request form
    //
    //	"images[]": "***** IMAGE FILES ****",
    //	"owner_type": "Company_or_User",
    //	"tags[]": "tag_id_1",
    //	"tags[]": "tag_id_2",
    //
    public function store(PartnerAssetStoreRequest $request, PartnerPage $partnerPage)
    {
        $loggedInUser = AuthService::getAuthUser();
        $customProperties = MediaHelper::setCustomProperties($request);
        $partnerAssets = PartnerAsset::create(
            [
                'model_id' => $loggedInUser->company_id,
                'model_type' => Company::class,
                'custom_properties' => $customProperties,
            ]
        );
        $images = collect($request->images);
        $images->each(function ($image) use ($partnerAssets, $customProperties) {
            $partnerAssets
                ->addMedia($image)
                ->withCustomProperties($customProperties)
                ->toMediaCollection(config('custom.media_collections.partner_page.partner_assets'));
        });
        $partnerAssets->load('media');

        return new PartnerAssetResource($partnerAssets);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/partner/page/{PARTNER_ASSET_ID}/asset/{MEDIA_ID}/delete",
     *     operationId="partner/page/asset/media/delete",
     *     tags={"PartnerAssets Controller"},
     *     summary="Delete a partner asset media",
     *     description="Delete a partner asset media",
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: Delete
    // http://127.0.0.1:8000/api/v1/partner/page/{PARTNER_ASSET_ID}/asset/{MEDIA_ID}/delete
    // Bearer token needed
    //

    public function deleteMedia(PartnerAsset $partnerAsset, PartnerMedia $partnerMedia)
    {
        $loggedInUser = AuthService::getAuthUser();
        if (!PartnerAssetService::userCanModify($loggedInUser, $partnerAsset)) {
            abort(403, config('genericMessages.error.CANNOT_DELETE_MEDIA_FROM_OTHER_USERS'));
        }
        if ($partnerMedia->model_id !== $partnerAsset->id) {
            abort(403, config('genericMessages.error.MEDIA_DOES_NOT_BELONG_TO_PARTNER_ASSET'));
        }
        $partnerMedia->delete();

        return response()->json();
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/partner/page/{PARTNER_ASSET_ID}/asset/delete",
     *     operationId="partner/page/asset/delete",
     *     tags={"PartnerAssetsController"},
     *     summary="Delete a partner asset",
     *     description="Delete a partner asset",
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: Delete
    // http://127.0.0.1:8000/api/v1/partner/page/{PARTNER_ASSET_ID}/asset/delete
    // Bearer token needed
    //

    public function delete(PartnerAsset $partnerAsset)
    {
        $loggedInUser = AuthService::getAuthUser();
        if (!PartnerAssetService::userCanModify($loggedInUser, $partnerAsset)) {
            abort(403, config('genericMessages.error.CANNOT_DELETE_MEDIA_FROM_OTHER_USERS'));
        }
        $partnerAsset->delete();

        return response()->json();
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/partner/page/{owner}/asset/by-msp",
     *     operationId="/partner/page/asset/showAssets",
     *     tags={"PartnerAssetsController"},
     *     summary="Show all partner assets from partner",
     *     description="Show all partner assets from partner",
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/partner/page/{owner}/asset/by-msp
    //OPTIONAL PARAMS
    /*
     *
        &paged=sometimes|required|boolean send 1 or 0
        &page=sometimes|required|numeric
        &items_per_page=sometimes|required|numeric
     * */
    // Bearer token needed
    //
    public function showAssets(AdminSearchRequest $request, Company $owner)
    {
        $query = PartnerAssetService::prepareShowAllQuery($request, $owner->id);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return PartnerAssetResource::collection($result);
    }
}
