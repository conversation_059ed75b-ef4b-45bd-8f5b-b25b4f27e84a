<?php

namespace App\Http\Controllers\Api\Review;

use App\Enums\Review\ReviewModelType;
use App\Enums\Review\ReviewQuestionCSVColumn;
use App\Enums\Review\ReviewQuestionType;
use App\Enums\Review\ReviewStatus;
use App\Helpers\ModelHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Review\RecentReviewsRequest;
use App\Http\Requests\Review\ReviewByUserShowAllRequest;
use App\Http\Requests\Review\ReviewDeleteRequest;
use App\Http\Requests\Review\ReviewPreCreateCheckRequest;
use App\Http\Requests\Review\ReviewQuestionShowAllRequest;
use App\Http\Requests\Review\ReviewStoreStepOneRequest;
use App\Http\Requests\Review\ReviewStoreStepTwoRequest;
use App\Http\Requests\Review\ReviewUpdateRequest;
use App\Http\Resources\Review\ReviewDetailedResource;
use App\Http\Resources\Review\ReviewQuestionResource;
use App\Http\Resources\Review\ReviewResource;
use App\Models\Company\Company;
use App\Models\Review\Review;
use App\Models\Review\ReviewAnswer;
use App\Models\Review\ReviewQuestion;
use App\Models\User;
use App\Notifications\FirstReviewApprovedNotification;
use App\Services\AuthService;
use App\Services\ImageService;
use App\Services\ProductService;
use App\Services\Review\ReviewAnswerService;
use App\Services\Review\ReviewService;
use App\Services\UserService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class ReviewController extends Controller
{
    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/review/questions",
     *     operationId="review/questions/loadQuestions",
     *     tags={"ReviewController"},
     *     summary="Get review questions list for a particular model_type",
     *     description="Get review questions list for a particular model_type",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/review/questions
    // OPTIONAL PARAMS
    /*
       ?model_type="required|string|allowedValues"
     *
     * */
    //needs Bearer Token
    public function loadQuestions(ReviewQuestionShowAllRequest $request)
    {
        $reviewQuestionsWithOptions = ReviewQuestion::with('activeOptions')
            ->where('model_type', ModelHelper::getClassByModelType($request->model_type))
            ->whereIn('question_type', [
                ReviewQuestionType::MultipleAnswer,
                ReviewQuestionType::RadioButton,
                ReviewQuestionType::SingleAnswer,
                ReviewQuestionType::StarRating,
                ReviewQuestionType::Scale,
            ])
            ->where('is_archived', false)
            ->whereHas('activeOptions');

        $reviewQuestions = ReviewQuestion::with('activeOptions')
            ->where('is_archived', false)
            ->where('model_type', ModelHelper::getClassByModelType($request->model_type))
            ->whereIn('question_type', [
                ReviewQuestionType::SingleLineText,
                ReviewQuestionType::Boolean,
                ReviewQuestionType::Date,
                ReviewQuestionType::OpenText,
            ])
            ->union($reviewQuestionsWithOptions)
            ->orderBy('order')
            ->get();

        return ReviewQuestionResource::collection($reviewQuestions);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/review/load-last-review",
     *     operationId="review/questions/loadLastReview",
     *     tags={"ReviewController"},
     *     summary="Load the last review for the model if exists",
     *     description="Load the last review for the model if exists",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/review/load-last-review
    //needs Bearer Token
    public function loadLastReview()
    {
        $loggedUser = AuthService::getAuthUser();
        $review = Review::where('reviewer_user_id', $loggedUser->id)
            ->orderBy('created_at', 'DESC')
            ->first();
        if (empty($review)) {
            return response()->json(config('genericMessages.error.NOT_FOUND'));
        }
        $review->load(['replies', 'reviewer', 'answers', 'answers.question', 'answers.option']);

        return new ReviewResource($review);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/review/store/step-one",
     *     operationId="review/storeStepOne",
     *     tags={"ReviewController"},
     *     summary="Store review first step.",
     *     description="Store review first step.",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/review/store/step-one
    /*
   {
        "title": "required|string|max:255|profanity",
        "model_id": "required|numeric",
        "model_type": "required|allowedValues",
        "incentivize": "required|boolean",
        "hide_reviewer_name": "required|boolean"
    }
     * */
    //needs Bearer Token
    public function storeStepOne(ReviewStoreStepOneRequest $request): ReviewResource
    {
        $modelExists = ModelHelper::idExistsInModel($request->model_type, $request->model_id);
        if (!$modelExists) {
            throw ValidationException::withMessages(
                [
                    'model_id' => config('genericMessages.error.MODEL_ID_DOES_NOT_EXIST_FOR_MODEL_TYPE'),
                ]
            );
        }
        $loggedUser = AuthService::getAuthUser();
        ReviewService::validateUserCanReview($loggedUser, $request->model_type, $request->model_id);
        Review::where("model_id", $request->model_id)->where("reviewer_user_id", $loggedUser->id)->delete();
        $review = Review::create([
            'reviewer_user_id' => $loggedUser->id, 'status' => ReviewStatus::abandoned, 'title' => $request->title,
            'model_id' => $request->model_id, 'model_type' => ModelHelper::getClassByModelType($request->model_type),
            'incentivize' => $request->incentivize,
            'read' => false,
            'product_user_verified' => false,
            'hide_reviewer_name' => $request->hide_reviewer_name,
        ]);
        $review->load(['reviewer', 'answers', 'answers.question', 'answers.option']);

        return new ReviewResource($review);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/review/store/step-two",
     *     operationId="review/storeStepTwo",
     *     tags={"ReviewController"},
     *     summary="Store review second step.",
     *     description="Store review first step.",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/review/store/step-two
    /*
   {
        "id": "required|numeric|exists:reviews,id",
        "job_title_id": "required|numeric|exists:job_titles,id",
        "recommended_by_reviewer_value": "required|numeric|min:0.5|max:10",
        "answers": "required|array|min:1",
        "answers.*.review_question_id": "required|numeric|exists:review_questions,id",
        "answers.*.review_question_option_ids": "sometimes|required|numericArray|exists:review_question_options,id",
        "answers.*.answer": "nullable|string",
    }
     * */
    //needs Bearer Token
    public function storeStepTwo(ReviewStoreStepTwoRequest $request): ReviewResource
    {
        $review = Review::find($request->id);
        $loggedUser = AuthService::getAuthUser();
        ReviewService::validateUserCanReview(
            $loggedUser, ModelHelper::getModelTypeByModelClass($review->model_type), $review->model_id);
        $questions = ReviewAnswerService::loadQuestionsFromAnswers($request->answers);
        $request->merge(['answers' => ReviewAnswerService::validateAnswers($request->answers, $questions)]);

        try {
            ReviewAnswer::where('review_id', $review->id)->delete();
            ReviewAnswerService::storeAnswers($review->id, $request->answers);
            ReviewAnswerService::validateIncentiveRegionAnswerExistenceOrSaveDefault($review->id);
        } catch (Exception $exc) {
            // $review->delete();
            throw ValidationException::withMessages([$exc->getMessage()]);
        }
        if ($loggedUser->job_title_id != $request->job_title_id) {
            $loggedUser->job_title_id = $request->job_title_id;
            $loggedUser->save();
        }
        $review->status = ReviewStatus::submitted;
        $review->recommended_by_reviewer_value = $request->recommended_by_reviewer_value;
        $review->save();
        $review->load(['reviewer', 'answers', 'answers.question', 'answers.option']);
        $reviewCount = Review::where("reviewer_user_id", $loggedUser->id)->count();
        if ($reviewCount == 1) {
            $loggedUser->notify(new FirstReviewApprovedNotification($loggedUser));
        }

        return new ReviewResource($review);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/review/update",
     *     operationId="review/update",
     *     tags={"ReviewController"},
     *     summary="Update a review",
     *     description="Update a review",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/review/update
    /*
   {
        "id": "required|numeric|exists:reviews,id",
        "title": "sometimes|required|string|max:255|profanity",
        "incentivize": "sometimes|required|boolean",
        "read": "sometimes|required|boolean",
        "product_user_verified": "sometimes|required|boolean",
        "recommended_by_reviewer_value": "sometimes|required|numeric|min:1|max:10",
        "hide_reviewer_name": "sometimes|required|boolean"
    }
     * */
    //needs Bearer Token
    public function update(ReviewUpdateRequest $request): ReviewResource
    {
        $loggedUser = AuthService::getAuthUser();
        $review = Review::find($request->id);

        // if the user is an admin then we don't need to change the status
        if (!AuthService::userIsSuperAdmin($loggedUser) && $review->reviewer_user_id == $loggedUser->id) {
            $review->status = ReviewStatus::underReview;
        }

        $review->update($request->validated());
        ReviewAnswerService::validateIncentiveRegionAnswerExistenceOrSaveDefault($review->id);

        return new ReviewResource($review);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/review/delete",
     *     operationId="review/delete",
     *     tags={"ReviewController"},
     *     summary="Delete a review",
     *     description="Delete a review",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/review/delete
    /*
   {
        "id": "required|numeric|exists:reviews,id"
    }
     * */
    //needs Bearer Token
    public function delete(ReviewDeleteRequest $request): JsonResponse
    {
        $loggedUser = AuthService::getAuthUser();
        $review = Review::find($request->id);
        UserService::validateUserIsOwnerOrSuperAdmin($loggedUser, $review->reviewer_user_id);
        $review->delete();

        return response()->json();
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/user/{USER_ID}/reviews/by-user",
     *     operationId="review/getReviewsByUser",
     *     tags={"ReviewController"},
     *     summary="Return all the user reviews created by the user",
     *     description="Return all the user reviews created by the user",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user/{user}/reviews/by-user
    // PARAMS
    /*
       ?paged=sometimes|required|boolean send 1 or 0
       &page=sometimes|required|numeric
       &items_per_page=sometimes|required|numeric|min:1
       &search_word=sometimes|required|string|min:3
       &read=sometimes|required|boolean
       &status[]=sometimes|required|array|AllowedValues
    / Bearer Token NEEDED*/
    public function getReviewsByUser(ReviewByUserShowAllRequest $request, User $user): AnonymousResourceCollection
    {
        $result = $this->getReviewByUserData($request, $user);

        return ReviewResource::collection($result);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/user/{user}/reviews/by-user-detailed",
     *     operationId="review/getReviewsByUserDetailed",
     *     tags={"ReviewController"},
     *     summary="Return all the user reviews created by the user with details",
     *     description="Return all the user reviews created by the user with details",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user/{user}/reviews/by-user-detailed
    // PARAMS
    /*
       ?paged=sometimes|required|boolean send 1 or 0
       &page=sometimes|required|numeric
       &items_per_page=sometimes|required|numeric|min:1
       &search_word=sometimes|required|string|min:3
       &read=sometimes|required|boolean
       &status[]=sometimes|required|array|AllowedValues
    / Bearer Token NEEDED*/
    public function getReviewsByUserDetailed(ReviewByUserShowAllRequest $request, User $user): AnonymousResourceCollection
    {
        return ReviewDetailedResource::collection($this->getReviewByUserData($request, $user));
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/reviews/most-recent",
     *     operationId="review/getMostRecent",
     *     tags={"ReviewController"},
     *     summary="Return the most recent reviews",
     *     description="Return the most recent reviews",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/reviews/most-recent
    // PARAMS
    /* ?items=sometimes|required|integer
    / Bearer Token NOT NEEDED*/
    public function getMostRecent(RecentReviewsRequest $request): array
    {
        $items = $request->get('items', config('common.reviews.default_recent_reviews_items'));
        $offset = $request->get('offset', 0);
        $nextOffset = $items + $offset;
        $reviews = ReviewService::getMostRecent($items, $offset);

        return [
            'next_offset' => $nextOffset,
            'reviews' => ReviewResource::collection($reviews),
        ];
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{COMPANY_ID}/reviews/export-csv",
     *     operationId="review/exportReviewsForcompany",
     *     tags={"ReviewController"},
     *     summary="export all the reviews for the company",
     *     description="export all the reviews for the company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/reviews/export-csv
    /*
    / Bearer Token NEEDED*/
    public function exportReviews(ReviewByUserShowAllRequest $request, Company $company)
    {
        $query = $this->prepareReview($request, $company->id);
        $reviews = $this->reviewCollection($request, $query);
        $reviewQuestions = ReviewQuestion::with('allOptions')
            ->where('question_key', '<>', 'JOB_TITLE')
            ->where('question_key', '<>', 'FOAGICAWHREARYOIN')
            ->orderBy('order')
            ->get();
        $columnsTitles = ReviewQuestionCSVColumn::getValues();
        $reviewQuestions->transform(function ($reviewQuestion) use ($company, &$columnsTitles) {
            $reviewQuestion->question = str_replace('{{vendorName}}', $company->name, $reviewQuestion->question);
            if ($reviewQuestion->question_type === ReviewQuestionType::MultipleAnswer) {
                $reviewQuestion->allOptions->map(function ($option) use (&$columnsTitles, $reviewQuestion) {
                    $columnsTitles['' . $reviewQuestion->id . '-' . $option->id] = $reviewQuestion->question . ' - ' . $option->display_value;
                });
            } else {
                $columnsTitles['' . $reviewQuestion->id] = $reviewQuestion->question;
            }

            return $reviewQuestion;
        });
        $data = [];
        foreach ($reviews as $row) {
            $data[] = array_map(function ($key) use ($row) {
                return $this->createReviewCsvRow($key, $row);
            }, array_keys($columnsTitles), $columnsTitles);
        }
        $fileName = 'vendor-reviews-' . time() . '.csv';

        return UtilityHelper::getCSVFileResponse($columnsTitles, $data, $fileName);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/review/{company}/all",
     *     operationId="review/showForCompany",
     *     tags={"ReviewController"},
     *     summary="Get all reviews for a particular company",
     *     description="Get all reviews for a particular company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/review/{company}/all
    // Bearer Token needed
    public function showForCompany(ReviewByUserShowAllRequest $request, Company $company): AnonymousResourceCollection
    {
        $query = $this->prepareReview($request, $company->id);

        return $this->reviewCollection($request, $query);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/review/approved",
     *     operationId="review/countOfApproved",
     *     tags={"ReviewController"},
     *     summary="Get count of approved reviews",
     *     description="Get count of approved reviews",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/review/approved
    // Bearer Token needed
    public function countOfApproved(): JsonResponse
    {
        $approvedCount = Review::select(DB::raw('count(reviews.id) AS number_of_reviews'))
            ->join('products', 'products.id', '=', 'reviews.model_id')
            ->where('reviews.status', ReviewStatus::approved)->first();

        return response()->json(['count' => $approvedCount->number_of_reviews]);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/review/pre-creation-check",
     *     operationId="review/preCreationCheck",
     *     tags={"ReviewController"},
     *     summary="Checks if the logged in user can create a new review",
     *     description="Checks if the logged in user can create a new review",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/review/pre-creation-check
    /*
   {
        "product_id": "required|string|max:255|profanity",
    }
     * */
    //needs Bearer Token
    public function preCreationCheck(ReviewPreCreateCheckRequest $request): array
    {
        $productId = $request->product_id;
        $loggedInUserId = AuthService::getLoggedInUserId();
        $userProductReviews = Review::where('reviewer_user_id', $loggedInUserId)
            ->where('model_id', $productId)
            ->whereNotIn('status', [ReviewStatus::abandoned])
            ->get();

        $flaggedCount = $userProductReviews->where('status', 'flagged')->count();
        $allButFlaggedCount = $userProductReviews->where('status', '!=', 'flagged')->count();

        // Confirm that the user does not have another review (can have one flagged review)
        if ($userProductReviews->count() === 0 || ($allButFlaggedCount === 0 && $flaggedCount <= 1)) {
            return [
                'can_create_review' => true,
            ];
        }

        return [
            'can_create_review' => false,
            'reason' => config('genericMessages.error.USER_ALREADY_HAS_REVIEWS_FOR_PRODUCT'),
            'reviews' => ReviewResource::collection($userProductReviews),
        ];
    }

    /**
     * Review collection
     */
    private function reviewCollection($request, $query): AnonymousResourceCollection
    {
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        $products = $pageResults->pluck('model')->filter();
        ProductService::calculateProductReviewsStatistics($products);
        ReviewService::setRatingsFromModels($pageResults, $products);
        ImageService::appendUsersAvatars($pageResults->pluck('reviewer')->filter());
        ImageService::appendCompanyAvatars($pageResults->pluck('reviewer.company')->filter());

        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return ReviewResource::collection($result);
    }

    /**
     * @throws ValidationException
     */
    private function prepareReviewsByUserQuery(ReviewByUserShowAllRequest $request, $userId)
    {
        $query = Review::with(['model', 'replies', 'answers', 'answers.question', 'answers.option'])
            ->where('status', '<>', ReviewStatus::archived)
            ->where('reviewer_user_id', $userId);

        if ($request->has('search_word')) {
            $query->whereRaw("(lower(title) like '%" .
                strtolower($request->search_word) . "%')");
        }
        if ($request->has('read')) {
            $query->where('read', $request->read);
        }
        if ($request->has('status')) {
            $query->whereIn('status', $request->status);
        }
        if ($request->has('model_type')) {
            $modelTypeClasses = [];
            foreach ($request->model_type as $modelType) {
                $modelTypeClasses[] = ModelHelper::getClassByModelType($modelType);
            }
            $query->whereIn('model_type', $modelTypeClasses);
        }

        return $query;
    }

    /**
     * prepare Review
     *
     * @throws ValidationException
     */
    private function prepareReview($request, $modelId)
    {
        $modelTypeClasses = [];
        $reviewsQuery = Review::with('model')
            ->where('reviews.status', ReviewStatus::approved)
            ->select('reviews.*');
        if ($request->has('model_type')) {
            $modelTypes = $request->model_type;
        } else {
            $modelTypes = ReviewModelType::getKeys();
        }
        foreach ($modelTypes as $modelType) {
            $modelTypeClass = ModelHelper::getClassByModelType($modelType);
            switch ($modelTypeClass) {
                case ReviewModelType::product:
                    $reviewsQuery->with(['answers', 'answers.question', 'answers.option'])
                        ->join('products', 'products.id', '=', 'reviews.model_id')
                        ->join('companies', 'companies.id', '=', 'products.company_id');

                    break;
                default:
                    throw ValidationException::withMessages([
                        'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $modelTypeClass,
                    ]);
            }
            $modelTypeClasses[] = $modelTypeClass;
        }

        $reviewsQuery->orderBy('reviews.created_at', 'desc');

        return $this->prepareCompanyValuesToQuery($request, $reviewsQuery, $modelId, $modelTypeClasses);
    }

    private function prepareCompanyValuesToQuery($request, $query, $companyId, $modelTypeClasses)
    {
        $query->with(['model', 'replies', 'reviewer', 'reviewer.company.enumType:id,type_is_of_vendor', 'reviewer.jobTitle'])
            ->where('companies.id', $companyId)
            ->whereNotNull('reviews.reviewer_user_id')
            ->whereIn('reviews.model_type', $modelTypeClasses);
        if ($request->has('search_word')) {
            $query->whereRaw("(lower(reviews.title) like '%" .
                strtolower($request->search_word) . "%')");
        }
        if ($request->has('read')) {
            $query->where('reviews.read', $request->read);
        }

        return $query;
    }

    /**
     * @throws ValidationException
     */
    private function getReviewByUserData(ReviewByUserShowAllRequest $request, User $user)
    {
        $loggedUser = AuthService::getAuthUser();
        if (!(UserService::profileUserIsCreatorOrAdmin($loggedUser, $user->id))) {
            throw ValidationException::withMessages(
                [
                    config('genericMessages.error.UNAUTHORIZED'),
                ]
            );
        }
        $query = $this->prepareReviewsByUserQuery($request, $user->id);

        return UtilityHelper::getSearchRequestQueryResults($request, $query);
    }

    private function createReviewCsvRow($key, $review)
    {
        return match ($key) {
            0 => $review->model->name,
            1 => $review->status,
            2 => $review->title,
            3 => $review->reviewer?->complete_name,
            4 => $review->reviewer?->jobTitle?->name,
            5 => $review->rating,
            6 => $review->recommended_by_reviewer_value,
            7 => $review->created_at,
            default => $this->setReviewCsvColumn($key, $review)
        };
    }

    private function setReviewCsvColumn($key, $review)
    {
        $parts = explode('-', $key);
        $answerTxt = '';
        if (count($parts) === 2) {
            $answer = $review->answers->where('review_question_option_id', $parts[1])->first();
        } else {
            $answer = $review->answers->where('review_question_id', $parts[0])->first();
        }
        if (!empty($answer)) {
            if (!empty($answer->review_question_option_id)) {
                $additionalInfo = '';
                if ($answer->option->show_answer_option === true) {
                    $additionalInfo = !empty($answer->option->show_answer_option_label)
                        ? ' - ' . $answer->option->show_answer_option_label . ': ' . $answer->answer
                        : ' - ' . $answer->answer;
                }
                $selectedAnswer = ($answer->question->question_type === ReviewQuestionType::MultipleAnswer)
                    ? 'X'
                    : $answer->option->display_value;
                $answerTxt = $selectedAnswer . $additionalInfo;
            } elseif (!empty($answer->answer)) {
                $answerTxt = $answer->answer;
            }
        }

        return $answerTxt;
    }
}
