<?php

namespace App\Http\Controllers\Api;

use App\Enums\VerificationMode;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\ConfirmEmailRequest;
use App\Http\Requests\ResendConfirmEmailRequest;
use App\Http\Requests\UserSearchRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Http\Resources\Company\CompanySmallResource;
use App\Http\Resources\UserResource;
use App\Http\Resources\UserSearchResource;
use App\Jobs\FinishUserConfirmation;
use App\Models\Company\Company;
use App\Models\PitchEvent\PitchEvent;
use App\Models\User;
use App\Models\UserProfileType;
use App\Services\AuthService;
use App\Services\HubspotService;
use App\Services\Notification\AdminUserNotificationService;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use stdClass;

class UserController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/user",
     *     operationId="getUser",
     *     tags={"UserController"},
     *     summary="Get logged user",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/UserUpdateRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user
    // needs Bearer Token*/
    public function getUser(Request $request): UserResource
    {
        return new UserResource($request->user()->load(
            ['company.avatar', 'company.enumType', 'company.companyProfileType', 'jobTitle']
        ));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/user/permissions",
     *     operationId="getUserPermissions",
     *     tags={"UserController"},
     *     summary="Get logged user permissions",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user/permissions
    // needs Bearer Token*/
    public function getUserPermissions(): JsonResponse
    {
        $loggedUser = AuthService::getAuthUser();
        $companyId = app('asCompanyId');
        $result = new StdClass();
        // if the user is a channel program admin the permissions must be the channel program admin permissions
        if (AuthService::userIsSuperAdmin($loggedUser)) {
            $result->permissions = ['CP_SUPER_ADMIN'];
            $result->is_super_admin = true;

            return response()->json($result);
        } else {
            $channelProgramPermissions = $loggedUser?->adminPermissionGroups(config('custom.channel_program_company.id'))
                ->pluck('key');
            $companyPermissions = $loggedUser?->permissionGroups($companyId)->pluck('key');

            // Checking for possible inherited permissions
            $company = Company::select('id', 'type')
                ->where('id', $companyId)
                ->with(['clientParent' => function ($query) {
                    $query->select('companies.id', 'company_clients.company_id');
                }])
                ->first();

            if (!empty($company) && !empty($company->clientParent)) {
                $inheritedPermissions = AuthService::getInheritedPermissionGroups(
                    $loggedUser->id,
                    $company?->type,
                    $company?->clientParent->id
                )->pluck('key');
                $companyPermissions = $companyPermissions->merge($inheritedPermissions);
            }

            $result->permissions = $channelProgramPermissions->merge($companyPermissions)->unique()->values();
        }
        $result->is_super_admin = $loggedUser?->claimedCompanies()->where('companies.id', $companyId)->exists();

        return response()->json($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/user",
     *     operationId="updateUser",
     *     tags={"UserController"},
     *     summary="Update user",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/UserUpdateRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/user
    // BODY
    /*{
          "id": "required|numeric|exists:users,id",
          "first_name": "sometimes|string|max:100",
          "last_name": "sometimes|string|max:100",
          "handle": "sometimes|string|unique",
          "description": "sometimes|required|string",
          "is_private": "sometimes|required|boolean",
          "phone": "string|max:25",
          "mobile_phone": "string|max:25",
          "job_title_id": "nullable|numeric|exits:job_titles,id",
          "event_email_subscribed": "sometimes|required|boolean",
          "country": "sometimes|required|string",
          "state": "sometimes|required|string",
          "city": "sometimes|required|string",
      }
    / needs Bearer Token*/
    public function updateUser(UserUpdateRequest $request): UserResource
    {
        $user = User::where('id', $request->get('id'))->first();
        if (($request->has('first_name') &&
                strtolower($request->get('first_name')) !== strtolower($user->first_name))
            ||
            ($request->has('last_name') &&
                strtolower($request->get('last_name')) !== strtolower($user->last_name))
        ) {
            $this->updateUserFriendlyUrl($user, $request);
            if (!empty($user->hubspot_contact_id)) {
                $firstName = $request->get('first_name') ?: $user->first_name;
                $lastName = $request->get('last_name') ?: $user->last_name;
                $properties = ['firstname' => $firstName, 'lastname' => $lastName];
                HubspotService::updateHubspotProperties($user->hubspot_contact_id, $properties);
            } else {
                Log::error('User (' . $user->id . ') does not have hubspot contact ID');
            }
        }
        $user->update($request->validated());
        if ($request->has('user_profile_type_value')) {
            $user = $this->updateUserProfileType($user, $request->get('user_profile_type_value'));
        }
        $user->load('jobTitle');

        return new UserResource($user);
    }

    private function updateUserFriendlyUrl($user, $request): void
    {
        $firstName = $request->get('first_name') ?: $user->first_name;
        $lastName = $request->get('last_name') ?: $user->last_name;
        $user->friendly_url = UtilityHelper::generateUniqueWord(
            'users',
            'friendly_url',
            UtilityHelper::generateUniqueWord(
                'companies',
                'friendly_url',
                $firstName . $lastName
            ),
            'id',
            $user->id
        );
        $user->save();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/confirm-email",
     *     operationId="confirmEmail",
     *     tags={"UserController"},
     *     summary="Confirm user email",
     *     description="Handle an incoming email confirmation request",
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/ConfirmEmailRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/confirm-email
    /*{
    	    "email": "THE_EMAIL",
    	    "expires": "112115441",
            "signature": "ASUUsushjasOPIUEqasjqQPPQS..."
    }
    no Bearer Token*/
    public function confirmEmail(ConfirmEmailRequest $request): JsonResponse
    {
        $user = $this->validateConfirmEmailUrl($request);

        return $this->processConfirmEmail($request, $user);
    }

    /**
     * This method validates the confirmation email URL
     *
     *
     * @throws ValidationException
     */
    private function validateConfirmEmailUrl(ConfirmEmailRequest $request): User
    {
        if (now()->timestamp > $request->expires) {
            throw ValidationException::withMessages([config('genericMessages.error.VERIFICATION_LINK_EXPIRED')]);
        }

        $user = User::where('email', '=', $request->email)->first();
        if (empty($user)) {
            throw ValidationException::withMessages([config('genericMessages.error.EMAIL_DOES_NOT_EXISTS')]);
        }

        if ($request->signature !== base64_encode($user->email . $user->password)) {
            throw ValidationException::withMessages([config('genericMessages.error.INVALID_VERIFICATION_LINK')]);
        }

        return $user;
    }

    /**
     * Process the email confirmation, if the link has not been used updates the email_verified_at in the DB
     * and sends an email to the user, else, the method informs the link has been used and the user can log in
     */
    private function processConfirmEmail(ConfirmEmailRequest $request, User $user): JsonResponse
    {
        if (!empty($user->email_verified_at)) {
            return response()->json(config('genericMessages.warning.VERIFICATION_LINK_ALREADY_USED'));
        }

        UserService::processPitchEventRegistration($request->pitch_event_id, $user);
        UserService::updateUserFields($user);
        FinishUserConfirmation::dispatch($user);

        return response()->json(config('genericMessages.success.VERIFICATION_SUCCESSFUL'));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/resend-confirm-email",
     *     operationId="resendConfirmEmail",
     *     tags={"UserController"},
     *     summary="Resend Confirm user email",
     *     description="Handle an incoming resend email confirmation request",
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/ResendConfirmEmailRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/resend-confirm-email
    // BODY
    /*{
            "email": "THE_EMAIL",
            "redirect_url": "https://www.yoururl.com"
            "pitch_event_id": "611515154484"
    }
    no Bearer Token*/
    public function resendConfirmEmail(ResendConfirmEmailRequest $request): JsonResponse
    {
        if ($request->pitch_event_id) {
            PitchEvent::findOrFail($request->pitch_event_id);
        }
        $user = User::where('email', '=', $request->email)->first();
        if ($user) {
            if (empty($user->email_verified_at)) {
                $user->sendEmailVerificationNotification($request->redirect_url);

                return response()->json(config('genericMessages.success.RESEND_VERIFICATION_SUCCESSFUL'));
            }

            throw ValidationException::withMessages([config('genericMessages.error.ACCOUNT_ALREADY_CONFIRMED')]);
        }

        throw ValidationException::withMessages([config('genericMessages.error.EMAIL_DOES_NOT_EXISTS')]);
    }

    private function updateUserProfileType(User $user, $newProfileTypeValue): User
    {
        $userProfileType = UserProfileType::firstWhere('value', $newProfileTypeValue);
        $user->userProfileType()->associate($userProfileType);
        $user->save();

        return $user;
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/user/send-confirm-email-code",
     *     operationId="sendConfirmEmailCode",
     *     tags={"UserController"},
     *     summary="Send verify user email code",
     *     description="Handle an outgoing email verification code",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/user/{user}/send-confirm-email-code
    // BODY
    /*{
    }
    no Bearer Token*/
    public function sendConfirmEmailCode(User $user): JsonResponse
    {
        if ($user->id !== AuthService::getLoggedInUserId()) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.UNAUTHORIZED')]
            );
        }
        if (empty($user->email_verified_at)) {
            $length = (int)config('common.plivo.codeSize');
            $user->verification_code = substr(('' . now()->getTimestamp()), -$length);
            $user->verification_code_expiration_date = now()->addMinutes(config('common.plivo.expirationMinutes'));
            $user->sendVerificationCode(VerificationMode::EMAIL);
            $user->verification_code_verified = false;
            $user->save();

            return response()->json(config('genericMessages.success.VERIFICATION_CODE_SUCCESSFULLY_SENT'));
        }

        throw ValidationException::withMessages([config('genericMessages.error.EMAIL_ALREADY_VERIFIED')]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/user/{user}verify-email-code",
     *     operationId="verifyEmailCode",
     *     tags={"UserController"},
     *     summary="Verify email code",
     *     description="Handle email code verification",
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user/{user}verify-email-code
    // PARAMETERS
    /*{
            "verification_code": "VERIFICATION_CODE",
    }
    no Bearer Token*/
    public function verifyEmailCode(User $user, Request $request): UserResource
    {
        if ($user->id !== AuthService::getAuthUser()->id) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.UNAUTHORIZED')]
            );
        }

        if (
            empty($user->verification_code) ||
            (int)$request->verification_code !== (int)$user->verification_code
        ) {
            throw ValidationException::withMessages([
                'verification_code' => config('genericMessages.error.INVALID_VERIFICATION_CODE'),
            ]);
        }

        if ($user->verification_code_verified) {
            throw ValidationException::withMessages([
                'verification_code' => config('genericMessages.error.VERIFIED_VERIFICATION_CODE'),
            ]);
        }

        if (now() > $user->verification_code_expiration_date) {
            throw ValidationException::withMessages([
                'verification_code' => config('genericMessages.error.EXPIRED_VERIFICATION_CODE'),
            ]);
        }

        $user->verification_code_verified = true;
        $user->email_verified_at = now();
        $user->save();

        return new UserResource($user);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/user/i-still-active",
     *     operationId="iStillActive",
     *     tags={"UserController"},
     *     summary="Tell backend the user still active",
     *     description="Tell backend the user still active",
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user/i-still-active
    // Bearer Token needed*/
    public function iStillActive(): JsonResponse
    {
        $userId = AuthService::getLoggedInUserId();
        AdminUserNotificationService::addOrUpdateUser($userId);

        return response()->json('date updated');
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/user/search",
     *     operationId="searchUser",
     *     tags={"UserController"},
     *     summary="Searching User for post tagging/mentioning",
     *     description="Searching User for post tagging/mentioning",
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user/search
    // Bearer Token needed*/
    public function searchUser(UserSearchRequest $request): AnonymousResourceCollection
    {
        $search = $request->q;
        $users = User::select('users.*', DB::raw("'user' as type"))
            ->where('is_private', false)
            ->whereNotNull('handle')
            ->where(function ($query) use ($search) {
                $query->where('first_name', 'ilike', '%' . $search . '%')
                    ->orWhere('last_name', 'ilike', '%' . $search . '%')
                    ->orWhere('friendly_url', 'ilike', '%' . $search . '%')
                    ->orWhere('handle', 'ilike', '%' . $search . '%');
            })
            ->orderBy('handle', 'asc')
            ->take(5)
            ->get();
        $companies = Company::select('companies.*', 'profile_vendor_handle as handle', DB::raw("'vendor' as type"))
            ->whereNotNull('profile_vendor_handle')
            ->where(function ($query) use ($search) {
                $query->where('name', 'ilike', '%' . $search . '%')
                    ->orWhere('profile_vendor_handle', 'ilike', '%' . $search . '%')
                    ->orWhere('friendly_url', 'ilike', '%' . $search . '%');
            })
            ->orderBy('name', 'asc')
            ->take(5)
            ->get();
        $searchResults = $users->merge($companies)->take(5);

        return UserSearchResource::collection($searchResults);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/user/companies",
     *     operationId="user/getCompanies",
     *     tags={"UserController"},
     *     summary="Get all the companies of a user",
     *     description="Get all the companies of a user",
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/user/companies
    // Bearer Token needed*/
    public function getCompanies()
    {
        $user = AuthService::getAuthUser();

        if (!$user) {
            return CompanySmallResource::collection(collect());
        }

        $user->load([
            'roles.company.avatar',
            'roles.company.enumType',
            'roles.company.clients:id',
            'roles.templateRole',
            'roles.templateRole.extends',
        ]);

        $roles = $user->roles;
        $companies = $roles->pluck('company');

        $extendedCompanyTypeIDs = $roles
            ->pluck('templateRole.extends')
            ->flatten()
            ->pluck('pivot.company_type_id')
            ->unique()
            ->filter();

        $parentCompaniesIDs = $companies->pluck('id')->filter();

        $inheritedCompanies = collect();
        if ($parentCompaniesIDs->isNotEmpty() && $extendedCompanyTypeIDs->isNotEmpty()) {
            $inheritedCompanies = Company::whereHas('clientParent', function ($query) use ($parentCompaniesIDs) {
                $query->whereIn('company_clients.company_id', $parentCompaniesIDs);
            })
                ->whereIn('companies.type', $extendedCompanyTypeIDs)
                ->select('id', 'name', 'friendly_url', 'is_distributor', 'subdomain', 'parent_id', 'type')
                ->with([
                    'avatar',
                    'enumType',
                ])
                ->get();
        }

        $allCompanies = $companies
            ->merge($inheritedCompanies)
            ->unique('id')
            ->values();

        $allCompanies->transform(function ($company) use ($roles) {
            $company->role = $roles->firstWhere('company_id', $company->id);
            $company->company_type = $company->enumType;

            return $company;
        });

        return CompanySmallResource::collection($allCompanies);
    }
}
