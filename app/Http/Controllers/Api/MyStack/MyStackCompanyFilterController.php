<?php

namespace App\Http\Controllers\Api\MyStack;

use App\Enums\MyStackCompanyFilter;
use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Http\Controllers\Controller;

class MyStackCompanyFilterController extends Controller
{
    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/my-stack/filters",
     *     operationId="my-stack/Filter",
     *     tags={"MyStackCompanyFilterController"},
     *     summary="Returns the my stack filters",
     *     description="Returns the my stack filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/my-stack/filters
    // needs Bearer Token
    public function filters()
    {
        $filters = MyStackCompanyFilter::asArray();
        $filters['rating']['items'] = $this->getRating();
        $filters['partner_flag']['items'] = $this->getPartnerFlag();
        $filters['partner_status']['items'] = $this->getPartnerStatus();

        return response()->json([
            'filters' => $filters,
        ]);
    }

    private function getPartnerFlag()
    {
        $partnerFlags = ['available' => 'Available', 'not_available' => 'Not Available'];
        $partnerFlagsArray = [];
        foreach ($partnerFlags as $key => $value) {
            $partnerFlagsArray[] = [
                'id' => $key,
                'name' => ucfirst($value),
            ];
        }

        return $partnerFlagsArray;
    }

    private function getPartnerStatus()
    {
        $partnerStatus = [
            PartnerPortalInvitationStatus::Accepted => 'Portal Access Granted',
            PartnerPortalInvitationStatus::Requested => 'Portal Request Pending',
            PartnerPortalInvitationStatus::getKey(null) => 'Portal Access Not Requested',
        ];
        $partnerStatusArray = [];
        foreach ($partnerStatus as $key => $value) {
            $partnerStatusArray[] = [
                'id' => ucfirst($key) ?? null,
                'name' => $value,
            ];
        }

        return $partnerStatusArray;
    }

    private function getRating()
    {
        $ratings = [0, 1, 2, 3, 4];
        $ratingArray = [];
        foreach ($ratings as $rating) {
            $ratingArray[] = [
                'id' => $rating,
                'name' => $rating,
            ];
        }

        return $ratingArray;
    }
}
