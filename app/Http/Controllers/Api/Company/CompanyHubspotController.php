<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Resources\HubspotCompanyResource;
use App\Services\HubspotService;

class CompanyHubspotController extends Controller
{
    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/search/company/hs/{companyName}",
     *     operationId="showCompanyHubspot",
     *     tags={"CompanyHubspotController"},
     *     summary="Get companies from hubspot",
     *     description="Returns the companies",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *
     *         @OA\JsonContent(ref="#/components/schemas/HubspotCompanyResource")
     *     ),
     *
     *     @OA\Parameter (
     *         name="company name",
     *         in="path",
     *         required=true,
     *         description="The company name",
     *
     *         @OA\Schema(
     *             type="string",
     *             format=""
     *         )
     *     ),
     * )
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/search/company/hs/channelp
    // No Bearer token needed
    public function showCompanyHubspot($companyName)
    {
        $results = HubspotService::searchCompanies($companyName);

        if ($results !== '') {
            return new HubspotCompanyResource($results);
        } else {
            // hubspot returned an error and we logged it in the service.
            return response('error', 500);
        }
    }
}
