<?php

namespace App\Http\Controllers\Api\Company;

use App\Enums\DefaultEmail;
use App\Helpers\CSVHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Http\Requests\Company\Marketplace\CompanyMarketplaceAddPartnersRequest;
use App\Http\Requests\Company\Marketplace\CompanyMarketplaceImportCSVRequest;
use App\Http\Requests\Company\Marketplace\CompanyMarketplaceImportCSVStatusRequest;
use App\Http\Requests\Company\Marketplace\CompanyMarketplacePartnerDeleteRequest;
use App\Http\Requests\Company\Marketplace\CompanyMarketplacePartnerShowAllRequest;
use App\Http\Requests\Company\Marketplace\CompanyMarketplacePartnerStoreRequest;
use App\Http\Requests\Company\Marketplace\CompanyMarketplacePartnerUpdateRequest;
use App\Http\Resources\Company\CompanyMarketplaceImportPartnersResource;
use App\Http\Resources\Company\CompanyMarketplacePartnerResource;
use App\Jobs\ProcessImportPartnersFromCSVJob;
use App\Models\Company\Company;
use App\Models\Company\CompanyMarketplacePartner;
use App\Notifications\MissingVendorDistributorNotification;
use App\Services\Advertisement\AdvertisementService;
use App\Services\AppConfig;
use App\Services\AuthService;
use App\Services\Company\CompanyMarketplaceService;
use App\Services\Company\CompanyService;
use App\Services\ImageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\ValidationException;

class CompanyMarketplacePartnerController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/marketplace",
     *     operationId="company/marketplace/showAll",
     *     tags={"CompanyMarketplacepartnerController"},
     *     summary="Show all marketplace partners for a particular company",
     *     description="Show all marketplace partners for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/marketplace
    /*{
    	"paged": "sometimes|required|boolean",
    	"page": "sometimes|required|numeric",
    	"items_per_page": "sometimes|required|numeric",
        "order_by": "sometimes|string|allowedValues"
        "sort": "sometimes|string|allowedValues"
        "search_word": "sometimes|string|min:3"
    }*/
    // NO Bearer token needed
    public function showAll(
        CompanyMarketplacePartnerShowAllRequest $request, Company $company): AnonymousResourceCollection
    {
        return $this->prepareMarketPlaceResponse($request, $this->prepareShowAllQuery($request, $company));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/distributors",
     *     operationId="company/marketplace/distributors",
     *     tags={"CompanyMarketplacepartnerController"},
     *     summary="Show all distributors for a particular company",
     *     description="Show all distributors for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/distributors
    /*{
    	"paged": "sometimes|required|boolean",
    	"page": "sometimes|required|numeric",
    	"items_per_page": "sometimes|required|numeric",
        "order_by": "sometimes|string|allowedValues"
        "sort": "sometimes|string|allowedValues"
        "search_word": "sometimes|string|min:3"
    }*/
    // NO Bearer token needed
    public function distributors(
        CompanyMarketplacePartnerShowAllRequest $request, Company $company): AnonymousResourceCollection
    {
        return $this->prepareMarketPlaceResponse($request, $this->prepareDistributorsQuery($request, $company));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/marketplace/store",
     *     operationId="company/marketplace/store",
     *     tags={"CompanyMarketplacepartnerController"},
     *     summary="Store a company in the company marketplace",
     *     description="Store a company in the company marketplace",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/marketplace/store
    /*
     {
        "partner_id": "required|numeric|exists:companies,id",
        "description": "nullable|string|max:2500"
        "external_url": ['nullable', 'string', 'max:2048', new CustomUrl()]"
     }
     * */
    // Bearer token needed
    public function store(
        CompanyMarketplacePartnerStoreRequest $request, Company $company): CompanyMarketplacePartnerResource
    {
        CompanyMarketplaceService::addNewPartner(
            $company->id, $request->partner_id, $request->description ?? "", $request->external_url);
        $result = $company->marketplacePartners()
            ->with('productsCategories')
            ->where('companies.id', $request->partner_id)
            ->get();
        $this->appendExtras($result);

        return new CompanyMarketplacePartnerResource($result->first());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/marketplace/add/csv",
     *     operationId="company/marketplace/add/csv",
     *     tags={"CompanyMarketplacepartnerController"},
     *     summary="Loop thru csv file and add partners relationship between Distributor and Vendors",
     *     description="Loop thru csv file and add partners relationship between Distributor and Vendors",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/marketplace/add/csv
    // BODY - form-data
    /*
    	csv_import: CSV FILE
    */
    // ADMIN Bearer token needed*/
    public function addPartnersFromCSV(CompanyMarketplaceImportCSVRequest $request, Company $company): JsonResponse
    {
        Log::debug('Entering CompanyMarketplacePartnerController::addPartnersFromCSV for company_id = ' . $company->id);
        $csvFile = $request->file('csv_import');
        if (($handle = fopen($csvFile, 'r')) !== false) {
            Log::debug('CompanyMarketplacePartnerController::addPartnersFromCSV parsing and validating csv');
            $columns = strtolower(preg_replace('/[^A-Za-z|]/', '', implode("|", CSVHelper::fgetcsv($handle))));
            if ($columns !== "vendorname|description|externalurl") {
                throw ValidationException::withMessages([
                    config('genericMessages.error.INVALID_CSV_FILE'),
                ]);
            }
            Log::debug('CompanyMarketplacePartnerController::addPartnersFromCSV getting company ist from CSV');

            $cacheToken = hash('sha256', $company->id . uniqid());
            Cache::put($cacheToken . "_status", "processing", 300);

            Log::debug('CompanyMarketplacePartnerController::addPartnersFromCSV saving temporary file');
            $filePath = storage_path('app/temp/');
            $csvFile->move($filePath, $cacheToken . '.csv');

            Log::debug('CompanyMarketplacePartnerController::addPartnersFromCSV dispatching Job');
            ProcessImportPartnersFromCSVJob::dispatch($cacheToken, $company->id);

            return response()->json([
                "token" => $cacheToken,
            ]);
        }

        throw ValidationException::withMessages([
            config('genericMessages.error.INVALID_CSV_FILE'),
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/marketplace/add/csv/status",
     *     operationId="company/marketplace/add/csv/status",
     *     tags={"CompanyMarketplacepartnerController"},
     *     summary="Check the processing status of the CSV file. Return both the status and the data if the processing has been completed.",
     *     description="Check the processing status of the CSV file. Return both the status and the data if the processing has been completed.",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/marketplace/add/csv/status
    // BODY - form-data
    /*
    	token: required|string|size:64
    */
    // ADMIN Bearer token needed*/
    public function getCSVProcessingStatus(CompanyMarketplaceImportCSVStatusRequest $request, Company $company): JsonResponse
    {
        $status = Cache::get($request->token . "_status");
        $data = $status !== 'complete' ? null : Cache::get($request->token . "_data");

        return response()->json([
            'status' => $status,
            'vendors' => $data,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/marketplace/add/bulk",
     *     operationId="company/marketplace/add/bulk",
     *     tags={"CompanyMarketplacepartnerController"},
     *     summary="Loop thru a list of companies and add partners relationship between Distributor and Vendors",
     *     description="Loop thru a list of companies and add partners relationship between Distributor and Vendors",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/marketplace/add/bulk
    // BODY - form-data
    /*
    	companies: [{
            name: Company name,
            description?: A description of the company
            company_id?: Company's ID
        }]
    */
    // ADMIN Bearer token needed*/
    public function addPartners(
        CompanyMarketplaceAddPartnersRequest $request, Company $company): AnonymousResourceCollection
    {
        Log::debug(
            'Entering CompanyMarketplacePartnerController::addPartnersFromCSV for company_id = ' . $company->id);
        $vendorsList = $request->validated();
        $requestedVendors = new Collection();
        $addedVendors = new Collection();
        foreach ($vendorsList['companies'] as $vendor) {
            Log::debug(json_encode($vendor, JSON_PRETTY_PRINT));
            if (!empty($vendor['company_id'])) {
                $vendorCompany = Company::find($vendor['company_id']);
                CompanyMarketplaceService::addNewPartner(
                    $company['id'], $vendor['company_id'], $vendor['description'] ?? '', true);
                $addedVendors->push((object)[
                    'name' => $vendor['name'],
                    'description' => $vendor['description'],
                    'vendor' => $vendorCompany,
                ]);
            } else {
                $requestedVendors->push((object)$vendor);
            }
        }
        // Send emails
        if (count($requestedVendors) > 0) {
            $loggedUser = AuthService::getAuthUser();
            $salesEmail = AppConfig::loadAppConfigByKey('SALES_EMAIL', DefaultEmail::SALES_EMAIL)
                ->value;
            $supportEmail = AppConfig::loadAppConfigByKey('SUPPORT_EMAIL', DefaultEmail::SUPPORT_EMAIL)
                ->value;
            if (!filter_var($salesEmail, FILTER_VALIDATE_EMAIL)) {
                $salesEmail = AppConfig::updateAppConfigByKey('SALES_EMAIL', DefaultEmail::SALES_EMAIL)->value;
            }
            if (!filter_var($supportEmail, FILTER_VALIDATE_EMAIL)) {
                $supportEmail = AppConfig::updateAppConfigByKey('SUPPORT_EMAIL', DefaultEmail::SUPPORT_EMAIL)->value;
            }

            Notification::route('mail', [$supportEmail, $salesEmail])
                ->notify(new MissingVendorDistributorNotification(
                    $requestedVendors->pluck('name')
                        ->toArray(), $loggedUser->complete_name, $loggedUser->email, $company->name));
        }
        ImageService::appendAvatars($addedVendors->pluck('vendor')->flatten()->unique()->filter());

        return CompanyMarketplaceImportPartnersResource::collection($addedVendors->merge($requestedVendors));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/{friendly_url}/marketplace/update",
     *     operationId="company/marketplace/update",
     *     tags={"CompanyMarketplacepartnerController"},
     *     summary="Update a company in the company marketplace",
     *     description="Update a company in the company marketplace",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/marketplace/update
    /*
     {
        "id": "required|numeric|exists:company_marketplace_partners,id",
        "description": "nullable|string|max:2500"
        "external_url": ['nullable', 'string', 'max:2048', new CustomUrl()]"
     }
     * */
    // Bearer token needed
    public function update(
        CompanyMarketplacePartnerUpdateRequest $request, Company $company): CompanyMarketplacePartnerResource
    {
        $companyMarketplacePartner = CompanyMarketplacePartner::findOrFail($request->id);
        $companyMarketplacePartner->update($request->validated());
        $result = $company->marketplacePartners()
            ->with('productsCategories')
            ->where('companies.id', $companyMarketplacePartner->partner_id)
            ->get();
        $this->appendExtras($result);

        return new CompanyMarketplacePartnerResource($result->first());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{friendly_url}/marketplace/delete",
     *     operationId="company/marketplace/delete",
     *     tags={"CompanyMarketplacepartnerController"},
     *     summary="Delete an array of companies to the company marketplace",
     *     description="Delete an array of companies to the company marketplace",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/marketplace/delete
    /*
     {
        "partners_ids": ["required|array|min:1|numeric|exists:companies,id"]
     }
     * */
    // Bearer token needed
    public function delete(CompanyMarketplacePartnerDeleteRequest $request, Company $company): JsonResponse
    {
        CompanyMarketplacePartner::whereIn('partner_id', $request->partners_ids)
            ->where('distributor_id', $company->id)
            ->delete();

        return response()->json();
    }

    private function appendExtras($result): void
    {
        ImageService::appendAvatars($result);
        CompanyService::calculateRatings($result);
        AdvertisementService::appendCompanyShowAsSponsored($result);
        CompanyService::appendProductsCategoriesToCollection($result);
    }

    private function prepareMarketPlaceResponse(
        CompanyMarketplacePartnerShowAllRequest $request, $query): AnonymousResourceCollection
    {
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        $pageResults->each(function ($company) {
            if ($company->pivot?->id === null) {
                unset($company->pivot);
            }
        });
        $this->appendExtras($pageResults);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return CompanyMarketplacePartnerResource::collection($result);
    }

    private function prepareShowAllQuery(AdminSearchRequest $request, Company $company)
    {
        return $company->marketplacePartners()
            ->with('productsCategories')
            ->whereHas('claimers')
            ->when($request->has('search_word'), function ($query) use ($request) {
                return $query->whereRaw("(lower(companies.name) like '%" .
                    strtolower($request->search_word) . "%' OR lower(companies.description) like '%" .
                    strtolower($request->search_word) . "%')");
            });
    }

    private function prepareDistributorsQuery(AdminSearchRequest $request, Company $company)
    {
        return $company->distributors()
            ->with('productsCategories')
            ->whereHas('claimers')
            ->when($request->has('search_word'), function ($query) use ($request) {
                return $query->whereRaw("(lower(companies.name) like '%" .
                    strtolower($request->search_word) . "%' OR lower(companies.description) like '%" .
                    strtolower($request->search_word) . "%')");
            });
    }
}
