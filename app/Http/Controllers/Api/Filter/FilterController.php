<?php

namespace App\Http\Controllers\Api\Filter;

use App\Enums\IndustryEventStatus;
use App\Enums\SubjectType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\CompanySearchRequest;
use App\Http\Requests\Filter\FilterIndustryEventsPresentersRequest;
use App\Http\Requests\Filter\FilterTopVideoCategoriesAndTagsRequest;
use App\Http\Resources\Category\CategoryResource;
use App\Http\Resources\Company\CompanyTypeResource;
use App\Http\Resources\CurrencyResource;
use App\Http\Resources\Filter\Company\AffiliateBrandsFilterResource;
use App\Http\Resources\Filter\CompanyProfileTypeFilterResource;
use App\Http\Resources\Filter\FilterIndustryEventsPresentersResource;
use App\Http\Resources\Filter\FilterTopVideoCategoriesAndTagsResource;
use App\Http\Resources\Filter\ProfileTypeFilterResource;
use App\Http\Resources\Filter\UserProfileTypeFilterResource;
use App\Models\AffiliateBrand\AffiliateBrand;
use App\Models\Company\CompanyType;
use App\Models\Currency;
use App\Models\IndustryEvent\IndustryEvent;
use App\Services\CategoryService;
use App\Services\FilterService;
use App\Services\TagService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class FilterController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/all-profile-types/",
     *     operationId="getAllProfileTypes",
     *     tags={"FiltersController"},
     *     summary="Get all profile types",
     *     description="Returns all profile types",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/all-profile-types
    // NO Bearer token needed
    public function getAllProfileTypes()
    {
        $profiles = collect();
        $profiles->put('user_profile_types', FilterService::findUserProfileTypes());
        $profiles->put('company_profile_types', FilterService::findCompanyProfileTypes());

        return new ProfileTypeFilterResource($profiles);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/user-profile-types",
     *     operationId="getUserTypes",
     *     tags={"FiltersController"},
     *     summary="Get all user types",
     *     description="Returns all user types",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/user-profile-types
    // NO Bearer token needed
    public function getUserProfileTypes(): AnonymousResourceCollection
    {
        return UserProfileTypeFilterResource::collection(FilterService::findUserProfileTypes());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/company-profile-types/",
     *     operationId="getCompanyProfileTypes",
     *     tags={"FiltersController"},
     *     summary="Get all user types",
     *     description="Returns all user types",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/company-profile-types
    // NO Bearer token needed
    public function getCompanyProfileTypes(): AnonymousResourceCollection
    {
        return CompanyProfileTypeFilterResource::collection(FilterService::findCompanyProfileTypes());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/currencies/",
     *     operationId="getCurrencies",
     *     tags={"FiltersController"},
     *     summary="Get all currncies",
     *     description="Returns all currencies",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/currencies
    // NO Bearer token needed
    public function getCurrencies(): AnonymousResourceCollection
    {
        $currencies = Currency::select(
            "id", "key", "symbol", "name", "description", "order", "created_at", "updated_at"
        )->orderBy('order')->get();

        return CurrencyResource::collection($currencies);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/products-categories/",
     *     operationId="getProductCategories",
     *     tags={"FiltersController"},
     *     summary="Get all categories of products that are been used by the companies",
     *     description="Returns all categories of products that are been used by the companies",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/products-categories/
    // NO Bearer token needed
    public function getProductsCategories(): AnonymousResourceCollection
    {
        $categories = DB::table('categories')
            ->whereIn('id', FilterService::findCategoriesIdsForProducts())
            ->orderBy('name')
            ->get();

        return CategoryResource::collection($categories);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/companies-categories/",
     *     operationId="getCompaniesCategories",
     *     tags={"FiltersController"},
     *     summary="Get all categories used by companies",
     *     description="Returns all categories used by companies",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/companies-categories/
    // NO Bearer token needed
    public function getCompaniesCategories(): AnonymousResourceCollection
    {
        $categories = DB::table('categories')
            ->whereIn('id', FilterService::findCategoriesIdsForCompanies())
            ->orderBy('name')
            ->get();

        return CategoryResource::collection($categories);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/vendors-categories/",
     *     operationId="getVendorsCategories",
     *     tags={"FiltersController"},
     *     summary="Get all categories used by vendors",
     *     description="Returns all categories used by vendors",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/vendors-categories/
    // NO Bearer token needed
    public function getVendorsCategories(): AnonymousResourceCollection
    {
        $categories = DB::table('categories')
            ->whereIn('id', FilterService::findCategoriesIdsForVendors())
            ->orderBy('name')
            ->get();

        return CategoryResource::collection($categories);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/affiliate-brands",
     *     operationId="getAffiliateBrand",
     *     tags={"FiltersController"},
     *     summary="Get affiliate brands",
     *     description="Get affiliate brands",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/affiliate-brands
    // NO Bearer token needed
    public function getAffiliateBrand(CompanySearchRequest $request): AnonymousResourceCollection
    {
        $affiliateBrands = AffiliateBrand::select('affiliate_brands.id', 'affiliate_brands.name',
            'affiliate_brands.main_company_id')
            ->where('affiliate_brands.is_active', true)
            ->when($request->has('ignore_ids'), function ($query) use ($request) {
                $query->whereNotIn('affiliate_brands.id', explode(',', $request->ignore_ids));
            })->when($request->has('name'), function ($query) use ($request) {
                return $query->whereRaw("(lower(affiliate_brands.name) LIKE '%" . strtolower($request->name) . "%')");
            })->when($request->has('has_main_company'), function ($query) use ($request) {
                if ($request->has_main_company) {
                    $query->join('companies', 'companies.id', 'affiliate_brands.main_company_id')
                        ->where('companies.manage_affiliates', true)
                        ->whereNotNull('affiliate_brands.main_company_id');
                } else {
                    $query->whereNull('affiliate_brands.main_company_id');
                }
            })->orderBy('affiliate_brands.name')->get();

        return AffiliateBrandsFilterResource::collection($affiliateBrands);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/company-types",
     *     operationId="getCompanyTypes",
     *     tags={"FiltersController"},
     *     summary="Get Company Types",
     *     description="Get Company Types",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/company-types
    // NO Bearer token needed
    public function getCompanyTypes(CompanySearchRequest $request): AnonymousResourceCollection
    {
        $companyTypes = CompanyType::select('*')
            ->when($request->has('type_is_of_vendor'), function ($query) use ($request) {
                $query->where('type_is_of_vendor', $request->type_is_of_vendor);
            })
            ->orderBy('label')
            ->get();

        return CompanyTypeResource::collection($companyTypes);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/videos-categories/",
     *     operationId="getVideosCategories",
     *     tags={"FiltersController"},
     *     summary="Get all categories used by videos",
     *     description="Returns all categories used by videos",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/videos-categories/
    // NO Bearer token needed
    public function getVideosCategories(): AnonymousResourceCollection
    {
        $categories = DB::table('categories')
            ->whereIn('id', FilterService::findCategoriesIdsForVideos())
            ->orderBy('name')
            ->get();

        return CategoryResource::collection($categories);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/used-categories/",
     *     operationId="getUsedCategories",
     *     tags={"FiltersController"},
     *     summary="Get all used categories in companies, products and videos",
     *     description="Returns all used categories in companies, products and videos",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/filters/used-categories/
    // NO Bearer token needed
    public function getUsedCategories(): AnonymousResourceCollection
    {
        $categoriesIds = FilterService::findCategoriesIdsForCompanies()
            ->merge(FilterService::findCategoriesIdsForProducts())
            ->merge(FilterService::findCategoriesIdsForVideos());

        $categories = DB::table('categories')
            ->whereIn('id', $categoriesIds->unique())
            ->orderBy('name')
            ->get();

        return CategoryResource::collection($categories);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/top-video-categories-and-tags",
     *     operationId="getTopVideoCategoriesAndTags",
     *     tags={"FiltersController"},
     *     summary="Get top video categories and tags",
     *     description="Returns top video categories and tags",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    /* http://127.0.0.1:8000/api/v1/filters/top-video-categories-and-tags
            ?category_items=# -- default_category_items
            &influencer_items=# -- default_influencer_items
    */
    // NO Bearer token needed
    public function getTopVideoCategoriesAndTags(FilterTopVideoCategoriesAndTagsRequest $request)
    {
        $categoryItems = $request->has('category_items') ?
            $request->category_items : config('common.filter.default_category_items');
        $tagItems = $request->has('tag_items') ?
            $request->tag_items : config('common.filter.default_tag_items');

        $results = collect();
        $results->put('top_categories', CategoryService::getTopVideoUsedCategories($categoryItems));
        $results->put('top_tags', TagService::getTopVideoUsedTags($tagItems));

        return new FilterTopVideoCategoriesAndTagsResource($results);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/filters/industry-calendar-presenters",
     *     operationId="getIndustryEventsPresenters",
     *     tags={"FiltersController"},
     *     summary="Get industry events presenters",
     *     description="Returns industry events presenters",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    // </editor-fold>
    // API Call: GET
    /* http://127.0.0.1:8000/api/v1/filters/industry-calendar-presenters
            ?search_word=sometimes|required|string|min:3
    */
    // NO Bearer token needed
    public function getIndustryEventsPresenters(FilterIndustryEventsPresentersRequest $request)
    {
        $results = IndustryEvent::select('companies.name')
            ->whereNotNull('companies.name')
            ->where('subject_type', SubjectType::companyProfile)
            ->join('companies', 'companies.id', '=', 'subject_id')
            ->where('status', IndustryEventStatus::approved)
            ->when($request->has('search_word'), function ($query) use ($request) {
                return $query->whereRaw('companies.name ilike ?', ['%' . strtolower(trim($request->search_word)) . '%']);
            })
            ->distinct()
            ->orderBy('companies.name')
            ->get();

        return FilterIndustryEventsPresentersResource::collection($results);
    }
}
