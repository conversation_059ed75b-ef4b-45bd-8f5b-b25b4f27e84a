<?php

namespace App\Http\Controllers\Api\Admin\Question;

use App\Enums\Question\QuestionType;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Question\QuestionOptionDeleteRequest;
use App\Http\Requests\Admin\Question\QuestionOptionStoreRequest;
use App\Http\Requests\Admin\Question\QuestionOptionUpdateRequest;
use App\Http\Resources\Admin\Question\AdminQuestionOptionResource;
use App\Models\Question\Question;
use App\Models\Question\QuestionOption;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AdminQuestionOptionController extends Controller
{
    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/questions/{question}/options",
     *     operationId="questions/{question}/options/showAll",
     *     tags={"AdminQuestionOptionController"},
     *     summary="Get question´s options list",
     *     description="Get question´s options list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/questions/{question}/options
    //needs Bearer Token
    public function showAll(Question $question)
    {
        $question->load('allOptions');

        return AdminQuestionOptionResource::collection($question->allOptions);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/questions/{question}/options/store",
     *     operationId="admin/questions/{question}/options/store",
     *     tags={"AdminQuestionOptionController"},
     *     summary="Store new question´s option",
     *     description="Creates a new question´s option",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/questions/{question}/options/store
    // BODY
    /*
    {
        "key": "required|string|max:100|profanity",
        "display_value": "required|string|max:255|profanity",
        "is_archived": "required|boolean",
        "option_order": "int",
        "show_answer_option": "not required|boolean"
    }
    needs Bearer Token*/
    public function store(QuestionOptionStoreRequest $request, Question $question)
    {
        try {
            $this->validateQuestionTypeHasNoOptions($question);
            $questionOption = QuestionOption::make($request->validated());
            $questionOption->question_id = $question->id;

            if ($request->has('is_archived')) {
                $questionOption = UtilityHelper::setArchivedAt($questionOption, $request->is_archived);
            }
            $questionOption->save();

            return new AdminQuestionOptionResource($questionOption);
        } catch (QueryException $ex) {
            $searchForMessage = 'duplicate key value violates unique constraint';
            if (Str::contains($ex->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.OPTION_ALREADY_EXISTS'),
                ]);
            }

            throw $ex;
        }
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/questions/{question}/options/update",
     *     operationId="questions/{question}/options/update",
     *     tags={"AdminQuestionOptionController"},
     *     summary="Update question´s option",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/questions/{question}/options/update
    // BODY
    /*{
        "id" : "required|numeric|exists:question_options,id",
        "key": "sometimes|required|string|max:100|profanity",
        "display_value": "sometimes|required|string|max:255|profanity",
        "is_archived": "sometimes|required|boolean"
    }*/
    // needs Bearer Token
    public function update(QuestionOptionUpdateRequest $request, Question $question)
    {
        try {
            $this->validateQuestionTypeHasNoOptions($question);
            $questionOption = QuestionOption::findOrFail($request->id);
            $this->validateOptionOwnership($questionOption, $question);
            if ($request->has('is_archived')) {
                $questionOption = UtilityHelper::setArchivedAt($questionOption, $request->is_archived);
            }
            $questionOption->update($request->validated());

            return new AdminQuestionOptionResource($questionOption);
        } catch (QueryException $ex) {
            $searchForMessage = 'duplicate key value violates unique constraint';
            if (Str::contains($ex->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.OPTION_ALREADY_EXISTS'),
                ]);
            }

            throw $ex;
        }
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/questions/{question}/options/delete",
     *     operationId="questions/{question}/options/delete",
     *     tags={"AdminQuestionOptionController"},
     *     summary="Delete question´s option",
     *     description="Deletes a record and returns no content",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/admin/questions/{question}/options/delete
    /* BODY
    {
    	"id" : "required|numeric|exists:question_options,id",
    }*/
    // needs Bearer Token
    public function delete(QuestionOptionDeleteRequest $request, Question $question): JsonResponse
    {
        try {
            $questionOption = QuestionOption::findOrFail($request->id);
            $this->validateOptionOwnership($questionOption, $question);

            $questionOption->is_archived = true;
            $questionOption = UtilityHelper::setArchivedAt($questionOption, true);
            $questionOption->save();

            return response()->json();
        } catch (QueryException $ex) {
            $searchForMessage = 'violates foreign key constraint';
            if (Str::contains($ex->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.REGISTER_HAS_DEPENDANT_VALUES'),
                ]);
            }

            throw $ex;
        }
    }

    private function validateQuestionTypeHasNoOptions($question)
    {
        if ($question->question_type === QuestionType::OpenText
            || $question->question_type === QuestionType::Date
            || $question->question_type === QuestionType::SingleLineText) {
            throw ValidationException::withMessages([
                config('genericMessages.error.OPTIONS_NOT_ALLOWED') . ': ' . $question->question_type,
            ]);
        }
    }

    private function validateOptionOwnership($questionOption, $question)
    {
        if ($questionOption->question_id != $question->id) {
            throw ValidationException::withMessages([
                'answers.question_id.' . $question->id . '.question_option_ids.' . $questionOption->id => config('genericMessages.error.OPTIONS_NOT_FROM_QUESTION'),
            ]);
        }
    }
}
