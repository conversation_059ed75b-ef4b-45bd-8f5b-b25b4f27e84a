<?php

namespace App\Http\Controllers\Api\Admin\Tag;

use App\Enums\AffiliateBrands\CategoriesFilters;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Tag\TagSearchRequest;
use App\Http\Requests\Tag\TagStoreRequest;
use App\Http\Requests\Tag\TagUpdateRequest;
use App\Http\Resources\Admin\Tag\AdminTagResource as AdminTagResource;
use App\Models\Tag;
use App\Services\TagService;
use DateTime;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use stdClass;

class AdminTagController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/tag/store",
     *     operationId="admin/tag/store",
     *     tags={"AdminTagController"},
     *     summary="Store new tag",
     *     description="Creates a new tag",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/TagStoreRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/tags/store
    // BODY
    /*{
        "name": "required, string, max:191, Profanity Validation",
        "is_hidden": "required, boolean SEND 1 or 0"
    }
    needs Bearer Token*/
    public function store(TagStoreRequest $request): JsonResponse
    {
        $tag = Tag::make($request->validated());
        $tag->save();

        return response()->json(new AdminTagResource($tag));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/tags",
     *     operationId="admin/tag/update",
     *     tags={"AdminTagController"},
     *     summary="Update tag",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(ref="#/components/schemas/TagUpdateRequest")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/tags
    // BODY
    /*{
        "id" : "required|numeric|exists:tags,id",
        "name": "sometimes|required|string|unique",
        "is_hidden": "sometimes|required|boolean (SEND 1 or 0)"
    }*/
    // needs Bearer Token
    public function update(TagUpdateRequest $request): JsonResponse
    {
        $tag = Tag::findOrFail($request->id);
        $tag->update($request->validated());

        return response()->json(new AdminTagResource($tag));
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/tags/search",
     *     operationId="admin/tag/search",
     *     tags={"AdminTagController"},
     *     summary="Search tags for administration page",
     *     description="Search tags for administration page",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/tags/search
    // Bearer Token needed
    public function search(TagSearchRequest $request): AnonymousResourceCollection
    {
        $query = $this->prepareShowAllQuery($request);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        $usageCountByMedia = TagService::getUsedByVideoCountList();
        $pageResults->transform(function ($tag) use ($usageCountByMedia) {
            $countObject = $usageCountByMedia->get($tag->id);
            $tag->usage_count = $countObject ? $countObject->count : 0;

            return $tag;
        });
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return AdminTagResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/tags/search/filters",
     *     operationId="admin/tag/search/searchFilters",
     *     tags={"AdminTagController"},
     *     summary="Get tag filters",
     *     description="Get tag filters",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws \Exception
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/tags/filters
    // needs Bearer Token
    public function filters()
    {
        $categories = Tag::select("created_at")->get();
        $filters = [];
        $result = [];
        if ($categories->count() > 0) {
            $filters["is_hidden"] = CategoriesFilters::is_hidden;
            $filters["is_hidden"]["items"][] = ["id" => 1, "name" => "Hidden"];
            $filters["is_hidden"]["items"][] = ["id" => 0, "name" => "Visible"];
            $filters["created_at"] = CategoriesFilters::created_at;
            $filters["created_at"]["items"] =
                $categories->unique("created_at")->map(function ($affiliateBrand) {
                    $nameDate = new Carbon(new DateTime($affiliateBrand->created_at));
                    $createdAt = new Carbon(new DateTime($affiliateBrand->created_at));
                    $dateObj = new stdClass();
                    $dateObj->id = $createdAt->format("Y-m-d");
                    $dateObj->name = $nameDate->format("M d, Y");

                    return $dateObj;
                })->unique("name")->sortByDesc("id")->values();
            $result = [
                "filters" => $filters,
            ];
        }

        return response()->json($result);
    }

    private function prepareShowAllQuery(TagSearchRequest $request): Builder
    {
        $query = Tag::select("id", "name", "is_hidden", "created_at")
            ->when($request->has('search_word'), function ($query) use ($request) {
                $lowerSearchWord = "%" . strtolower($request->search_word) . "%";
                $query->whereRaw("(lower(name) like ?)", [$lowerSearchWord]);
            })->when($request->has("created_at"), function ($query) use ($request) {
                $query->whereIn(DB::raw("DATE(created_at)"), $request->get("created_at"));
            })->when($request->has("is_hidden"), function ($query) use ($request) {
                $query->where("is_hidden", (bool)$request->is_hidden);
            });

        return $query;
    }
}
