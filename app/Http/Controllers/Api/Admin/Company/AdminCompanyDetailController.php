<?php

namespace App\Http\Controllers\Api\Admin\Company;

use App\Enums\ActivityLogAction;
use App\Enums\Company\CompanyProfileTypes;
use App\Enums\ModelType;
use App\Enums\User\CompanyUsersAndClaimersFilters;
use App\Enums\UserStatus;
use App\Helpers\HubspotHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Company\AdminCompanyChangeSubcriptionHSRequest;
use App\Http\Requests\Admin\Company\AdminCompanyChangeSubcriptionRequest;
use App\Http\Requests\Admin\Company\AdminCompanyUpdateRequest;
use App\Http\Requests\Admin\User\AdminAddUserToCompanyRequest;
use App\Http\Requests\Admin\User\AdminRemoveUserToCompanyRequest;
use App\Http\Requests\Admin\User\AdminUserRequest;
use App\Http\Resources\Admin\Company\AdminCompanyDetailResource;
use App\Http\Resources\Admin\Company\AdminCompanyUserAndClaimersResource;
use App\Http\Resources\Company\HubspotDealResource;
use App\Models\Company\Company;
use App\Models\Company\CompanyClaimer;
use App\Models\Company\CompanyType;
use App\Models\Country;
use App\Models\Permission\Role\Role;
use App\Models\Permission\Role\RoleUser;
use App\Models\Profile\CompanyProfileType;
use App\Models\State;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\AuthService;
use App\Services\BusinessRulesService;
use App\Services\Company\CompanyProfileTypeService;
use App\Services\Company\CompanyService;
use App\Services\Company\CompanyTypeService;
use App\Services\FilterService;
use App\Services\HubspotService;
use App\Services\ImageService;
use App\Services\Permission\PermissionService;
use App\Services\Permission\RoleUserService;
use App\Services\UserService;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class AdminCompanyDetailController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/admin/company/{COMPANY_ID}/update",
     *     operationId="/admin/company/{COMPANY_ID}/update",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Update company",
     *     description="Returns a Company instance",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/admin/company/{COMPANY_ID}/update
    // BODY
    /*{
        "name" => ["sometimes", "string", "max:191", profanity, UniqueField],
        "description" : "string|max:4000",
        "company_type" : "sometimes|required|exists:company_types,value",
        "revenue" : "numeric",
        "subdomain" : "sometimes|required|string|min:3|max:255",
        "address" : "nullable|string|max:255",
        "address2" : "nullable|string|max:255",
        "city" : "nullable|string|max:255",
        "state_id" : "nullable|numeric|exists:states,id",
        "zip" : "nullable|string|max:10",
        "country_id" : "nullable|numeric|exists:countries,id",
        "phone" : "nullable|string|max:25",
        "industry" : "nullable|string|max:255",
        "employee_range" : "nullable|string|max:255",
        "founded" : "nullable|numeric|between:1900,$currentYear",
        "features" : "nullable|array",
        "profile_company_website_url" : "nullable|string|max:100",
        "profile_vendor_handle" : "sometimes|required|string|no_white_spaces|min:5|max:50|unique",
        "profile_company_friendly_name" : "nullable|string|max:50",
        "show_distributor_banner": "sometimes|required|boolean",
        "show_manage_clients_banner": "sometimes|required|boolean",
        "parent_id" : "nullable|numeric|exists:companies,id",
        "affiliate_id" : "nullable|string|max:255",
        "is_distributor": "sometimes|boolean",
        "manage_clients": "sometimes|boolean",
        "manage_affiliates": "sometimes|boolean",
        "company_profile_types_id" : "sometimes|numeric|exists:company_profile_types,id",
        "state" : "nullable|string|max:255",
        "country" : "nullable|string|max:255",
    }*/
    // needs Bearer Token
    public function update(AdminCompanyUpdateRequest $request, Company $company)
    {
        $company->load(['companyClaimers', 'enumType', 'companyProfileType', 'affiliates']);
        if ($request->has('is_distributor') && (bool)$request->is_distributor) {
            if ($request->has('company_type')) {
                $companyType = CompanyType::firstWhere('value', $request->company_type);
                CompanyTypeService::validateTypeIsOfVendor($companyType);
            } else {
                CompanyService::validateCompanyIsVendor($company);
            }
        }
        if ($request->has('manage_clients') && (bool)$request->manage_clients) {
            if ($request->has('company_type')) {
                $companyType = CompanyType::firstWhere('value', $request->company_type);
                CompanyTypeService::validateTypeIsOfMSP($companyType);
            } else {
                CompanyService::validateCompanyIsMSP($company);
            }
        }
        if ($request->has('profile_vendor_handle')) {
            $response = Gate::inspect('update-company-handle', $company);
            if (!$response->allowed()) {
                abort(403, $response->message());
            }
        }
        if ($request->has('name')) {
            $response = Gate::inspect('update-company-name', $company);
            if (!$response->allowed()) {
                abort(403, $response->message());
            }
            if (strtolower($request->get('name')) !== strtolower($company->name)) {
                CompanyService::updateCompanyFriendlyUrl($company, $request);
            }
        }
        if ($request->has('subdomain')) {
            if (strtolower($request->get('subdomain')) !== strtolower($company->subdomain)) {
                $request->merge(['subdomain' => strtolower($request->get('subdomain'))]);
            }
        }
        if ($request->has('state')) {
            $state = State::select('id')->where('name', $request->state)
                ->orWhere('abbreviation', $request->state)->first();
            if (!empty($state)) {
                $request->merge(['state_id' => $state->id]);
            }
        }
        if ($request->has('country')) {
            $country = Country::select('id')->where('short_code', $request->country)
                ->orWhere('title', $request->country)->first();
            if (!empty($country)) {
                $request->merge(['country_id' => $country->id]);
            }
        }
        if ($request->has('company_type')) {
            CompanyTypeService::updateCompanyType($company, $request->company_type);
        }
        DB::beginTransaction();

        try {
            $companyData = $request->all();
            unset($companyData['company_profile_types_id']);
            $company->update($companyData);

            if ($request->has('company_profile_types_id')) {
                $newCompanyProfileType = CompanyProfileType::find($request->company_profile_types_id);
                CompanyProfileTypeService::changeSubscription($company, $newCompanyProfileType, true);
            }
            DB::commit();

            return new AdminCompanyDetailResource($this->getCompanyDetails($company));
        } catch (ValidationException $e) {
            Log::error(__CLASS__ . '::' . __FUNCTION__ . ' - Error updating company');
            Log::error($e->getMessage());
            DB::rollBack();

            throw ValidationException::withMessages([$e->getMessage()]);
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/company/{COMPANY_ID}/subscription",
     *     operationId="subscription/showAll",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Get companies list",
     *     description="Get companies list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/company/{COMPANY_ID}/subscription

    /*
     {
        "company_profile_type_id" : "required|numeric|exists:company_profile_types,id"
     }
     */
    // needs Bearer Token
    public function changeSubscription(AdminCompanyChangeSubcriptionRequest $request, Company $company): JsonResponse
    {
        $newCompanyProfileType = CompanyProfileType::find($request->company_profile_type_id);
        CompanyProfileTypeService::changeSubscription($company, $newCompanyProfileType, true);

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/company/subscription",
     *     operationId="hs/subscription",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Save new subscription and make user claimer",
     *     description="Save new subscription and make user claimer",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws InvalidEnumMemberException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/company/hs/subscription

    /*
     {
        "email": "required",
        "company_profile_type_id" : "optional|numeric|exists:company_profile_types,id"
     }
     */
    // needs Bearer Token
    public function changeSubscriptionHS(AdminCompanyChangeSubcriptionHSRequest $request)
    {
        // get company from the users email
        $user = User::with('company')->where('email', trim(strtolower($request->email)))->first();

        if ($user === null) {
            throw ValidationException::withMessages([config('genericMessages.error.NOT_FOUND')]);
        }

        // if not company profile type sent in, make them MSP BUSINESS PREMIUM
        $companyProfileTypeId = $request->company_profile_type_id;
        if (empty($companyProfileTypeId)) {
            $companyProfileType = CompanyProfileType::where('value', CompanyProfileTypes::MSPBusinessPremium)->first();
        } else {
            $companyProfileType = CompanyProfileType::find($request->company_profile_type_id);
        }

        // change subscription
        if ($user->company->company_profile_types_id !== $companyProfileType->id) {
            CompanyProfileTypeService::changeSubscription($user->company, $companyProfileType);
        }

        // make user a claimer
        $claimer = CompanyClaimer::where('user_id', $user->id)->where('company_id', $user->company->id)->count();
        if ($claimer === 0) {
            PermissionService::addSuperAdminForCompany($user->company, $user, $user->company->superAdminRole()->id);
        }

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/{COMPANY_ID}/details",
     *     operationId="subscription/showDetails",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Get company details",
     *     description="Get company details",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/company/{COMPANY_ID}/details
    // needs Bearer Token
    public function showDetails(Company $company): AdminCompanyDetailResource
    {
        return new AdminCompanyDetailResource($this->getCompanyDetails($company));
    }

    private function getCompanyDetails(Company $company): array
    {
        $company->load([
            'enumType' => function ($query) {
                $query->select('company_types.id', 'company_types.label', 'company_types.value', 'company_types.order',
                    'company_types.type_is_of_vendor', 'company_types.default_company_profile_type_id');
            },
            'parentCompany' => function ($query) {
                $query->select('companies.id', 'companies.name', 'companies.created_at', 'companies.updated_at');
            },
        ]);
        $profileTypes = CompanyProfileType::all();
        $profileTypes->transform(function ($type) {
            $type->is_default = in_array($type->value, [CompanyProfileTypes::VendorFree,
                CompanyProfileTypes::MSPBusinessBasic, CompanyProfileTypes::DirectBasic], true);

            return $type;
        });
        $businessRules = BusinessRulesService::getConfigRulesAndValues();
        $activeSubscription = CompanyProfileTypeService::getCompanyActiveSubscription($company);
        ImageService::appendAvatars(collect([$company]));
        $company->is_affiliate_brand_main_company = $company->isAffiliateBrandMainCompany();

        return [
            'company' => $company,
            'profile_types' => $profileTypes,
            'business_rules' => $businessRules,
            'active_subscription' => $activeSubscription,
        ];
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/{COMPANY_ID}/users-and-claimers",
     *     operationId="/api/v1/admin/company/{COMPANY_ID}/users-and-claimers",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Get company users and claimers",
     *     description="Get company users and claimers",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // /api/v1/admin/company/{COMPANY_ID}/users-and-claimers
    /*
        ?paged=sometimes|required|boolean
        &page=sometimes|required|numeric
        &items_per_page=sometimes|required|numeric|min:1
        &order_by=COLUMN_NAME
        &sort=SORT_VALUE
        &search_word=SEARCH_VALUE
     * */
    // needs Bearer Token
    public function getCompanyUsersAndClaimers(AdminUserRequest $request, Company $company): AnonymousResourceCollection
    {
        $result = UserService::getActiveUsersForACompany($request, $company);

        return AdminCompanyUserAndClaimersResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/company/{COMPANY_ID}/users-and-claimers/filters",
     *     operationId="/api/v1/admin/company/{COMPANY_ID}/users-and-claimers/filters",
     *     tags={"AdminCompanyDetailsController"},
     *     summary="Get filters for the company's users and claimers list",
     *     description="Get filters for the company's users and claimers list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // /api/v1/admin/company/{COMPANY_ID}/users-and-claimers/filters
    /**
     * NO PARAMS NEEDED
     */
    // needs Bearer Token
    public function getCompanyUsersAndClaimersFilters(Company $company): JsonResponse
    {
        $filters['status'] = CompanyUsersAndClaimersFilters::STATUS;
        $filters['status']['items'] = array_map(function ($key) {
            return [
                'id' => UserStatus::getValue($key),
                'name' => $key,
            ];
        }, UserStatus::getKeys());

        $filters['roles'] = CompanyUsersAndClaimersFilters::ROLES;
        $filters['roles']['items'] = Role::select('id', 'display_name')->where('company_id', $company->id)
            ->orderBy('display_name')->get()->map(function ($role) {
                return [
                    'id' => '' . $role->id,
                    'name' => $role->display_name,
                ];
            });

        $userIds = RoleUser::select('roles_users.user_id')
            ->usersByCompanyRoles($company->id)
            ->get()
            ->pluck('user_id')
            ->toArray();

        $users = User::select('last_logged_in_at', 'created_at')->whereIn('id', $userIds)
            ->get();

        $filters['last_login'] = CompanyUsersAndClaimersFilters::LAST_LOGIN;
        $filters['last_login']['items'] = FilterService::prepareItemDateField($users, 'last_logged_in_at');

        $filters['registered_date'] = CompanyUsersAndClaimersFilters::REGISTERED_DATE;
        $filters['registered_date']['items'] = FilterService::prepareItemDateField($users);

        return response()->json([
            'filters' => $filters,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/company/{COMPANY_ID}/sync-company-id",
     *     operationId="subscription/syncHubspotId",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Get hubspot ID from Hubspot",
     *     description="Sends an http request to Hubspot to fetch company's ID and udates the value on the database",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/company/{COMPANY_ID}/sync-company-id
    // needs Bearer Token
    public function syncHubspotId(Company $company)
    {
        $users = $company->users->reject(function ($value, $key) {
            return empty($value->hubspot_contact_id);
        });

        if ($users->isEmpty()) {
            abort(422, 'Could not fetch company Hubspot ID, company has no users associated');
        }

        $updatedHubspotId = false;
        foreach ($users as $u) {
            try {
                $companyHubspotId = HubspotService::findHubspotCompanyIdByContactId($u->hubspot_contact_id);
                if ($companyHubspotId !== '0') {
                    $company->hubspot_id = $companyHubspotId;
                    $company->save();
                    $updatedHubspotId = true;

                    break;
                }
            } catch (\Throwable $exception) {
                info('Could not fetch company Hubspot ID, ' . $exception->getMessage());
            }
        }

        if (!$updatedHubspotId) {
            abort(422, 'Could not fetch company Hubspot ID, check logs');
        }

        return ['hubspot_id' => $company->hubspot_id];
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/company/{COMPANY_ID}/add-user-role",
     *     operationId="company/addUserRole",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Add user with role to a company",
     *     description="Add user with role to a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/company/{COMPANY_ID}/add-user-role
    // Bearer Token NEEDED
    public function addUserRole(Company $company, AdminAddUserToCompanyRequest $request)
    {
        $roleId = $request->get('role_id');
        $user = User::select('id', 'email')->where('id', $request->get('user_id'))->firstOrFail();
        RoleUserService::updateUserRoleByCompany($company, $roleId, $user);

        ActivityLogsService::store(
            ActivityLogAction::addedUser,
            ModelType::userType,
            AuthService::getLoggedInUserId(),
            ModelType::companies,
            $company->id,
            [
                'role_id' => '' . $roleId,
                'user_id' => '' . $user->id,
                'company_id' => '' . $company->id,
            ]
        );

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/company/{COMPANY_ID}/update-user-role",
     *     operationId="company/updateUserRole",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Update user with role to a company",
     *     description="Update user with role to a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/company/{COMPANY_ID}/update-user-role
    /*
     "role_id": "required|exists:roles,id",
     "user_id": "required|exists:users,id",
    / Bearer Token NEEDED*/
    public function updateUserRole(Company $company, AdminAddUserToCompanyRequest $request)
    {
        $user = User::select('id', 'email')->where('id', $request->get('user_id'))->firstOrFail();
        $roleId = $request->get('role_id');
        $prevRoleId = $request->get('prev_role_id');

        RoleUserService::updateUserRoleByCompany($company, $roleId, $user, $prevRoleId);

        ActivityLogsService::store(
            ActivityLogAction::updatedUser,
            ModelType::userType,
            AuthService::getLoggedInUserId(),
            ModelType::companies,
            $company->id,
            [
                'role_id' => '' . $roleId,
                'user_id' => '' . $user->id,
                'company_id' => '' . $company->id,
                'prev_role_id' => '' . $prevRoleId,
            ]
        );

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/company/{COMPANY_ID}/remove-user-role",
     *     operationId="company/removeUserRole",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Remove user role from a company",
     *     description="Remove user role from a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/admin/company/{COMPANY_ID}/remove-user-role
    // Bearer Token NEEDED
    public function removeUserRole(Company $company, AdminRemoveUserToCompanyRequest $request)
    {
        RoleUserService::deleteUserRoleByCompany($company, $request->get('user_id'), $request->get('role_id'));

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/company/{COMPANY_ID}/pull-hubspot-deals",
     *     operationId="subscription/pullCompanyHubspotDeals",
     *     tags={"AdminCompanyDetailController"},
     *     summary="Pull company deals from Hubspot",
     *     description="Pulls all company deals from Hubspot and stores them on CP database",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=false
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/admin/company/{COMPANY_ID}/pull-hubspot-deals
    // needs Bearer Token
    public function pullCompanyHubspotDeals(Company $company)
    {
        $searchResult = HubspotService::findCompanyHubspotDeals($company);
        $deals = $searchResult['results'];
        $irregularDeals = [];
        foreach ($deals as $deal) {
            $processResult = HubspotHelper::processHubspotDeal($deal, $company);

            if ($processResult['status'] === false) {
                $irregularDeals[] = [
                    'reason' => $processResult['errorMessage'],
                    'hubspot_deal' => $deal,
                ];
            }
        }

        if (count($irregularDeals) > 0) {
            Log::warning('SyncAllHubspotDeals:Job Ended: There were Hubspot Deals that could not be saved', $irregularDeals);
        }

        return HubspotDealResource::collection($deals);
    }
}
