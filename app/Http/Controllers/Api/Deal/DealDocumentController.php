<?php

namespace App\Http\Controllers\Api\Deal;

use App\Enums\ActivityLogAction;
use App\Enums\Deals\DealDocumentTypes;
use App\Enums\MediaType;
use App\Enums\ModelType;
use App\Helpers\MediaHelper as MediaHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Deal\Documents\DealDocumentDeleteRequest;
use App\Http\Requests\Deal\Documents\DealDocumentStoreRequest;
use App\Http\Requests\Deal\Documents\DealDocumentUpdateRequest;
use App\Http\Requests\Deal\Documents\DealDocumentUploadRequest;
use App\Http\Resources\Deal\DealDocumentResource;
use App\Models\Company\Company;
use App\Models\Deal\Deal;
use App\Models\Deal\DealDocument;
use App\Models\User;
use App\Services\ActivityLogs\ActivityLogsService;
use App\Services\AuthService;
use App\Services\Deal\DealService;
use App\Services\MediaService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class DealDocumentController extends Controller
{
    private function checkPermissions(Company $company, Deal $deal): User
    {
        $loggedUser = AuthService::getAuthUser();
        DealService::validateDealIsFromCompany($company, $deal);

        return $loggedUser;
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/deals/{company}/deal/{deal}/documents",
     *     operationId="deals/documents/showAll",
     *     tags={"DealDocumentController"},
     *     summary="Get all documents for a deal",
     *     description="Get all documents for a deal",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: GET
    // {{server}}api/v1/deals/{company}/deal/{deal}/documents
    // Bearer token needed
    public function showAll(Company $company, Deal $deal): AnonymousResourceCollection
    {
        $this->checkPermissions($company, $deal);
        $deal->load([
            'dealDocuments.mediaFile',
            'dealDocuments.company:id,name,type',
            'dealDocuments.company.enumType',
            'dealDocuments.user:id,first_name,last_name',
        ]);

        return DealDocumentResource::collection($deal->dealDocuments);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/deals/{company}/deal/{deal}/documents/store",
     *     operationId="deals/documents/storeDocument",
     *     tags={"DealDocumentController"},
     *     summary="Store deal document for a company",
     *     description="Store deal document for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws Exception
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}api/v1/deals/{company}/deal/{deal}/documents/store
    // Bearer token needed
    public function storeDocument(DealDocumentStoreRequest $request, Company $company, Deal $deal): DealDocumentResource
    {
        $loggedUser = $this->checkPermissions($company, $deal);
        $dealDocument = $deal->dealDocuments()->create([
            'company_id' => $company->id,
            'user_id' => $loggedUser->id,
            'type' => DealDocumentTypes::document,
            'name' => $request->name ?? '',
        ]);

        return new DealDocumentResource($dealDocument);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/deals/{company}/deal/{deal}/documents/upload",
     *     operationId="deals/documents/uploadDocument",
     *     tags={"DealDocumentController"},
     *     summary="Upload deal document or chunks for a company",
     *     description="Upload deal document or chunks for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}api/v1/deals/{company}/deal/{deal}/documents/upload
    /*
    {
        "id":"required|numeric|exists:deal_documents,id|isFromDeal"
        "document":"required|file|max:1000000|mimeType:document"
        "title":"sometimes|string|max:255|profanityCheck"
    }
    */
    // Bearer token needed
    public function uploadDocument(
        DealDocumentUploadRequest $request, Company $company, Deal $deal): JsonResponse
    {
        $loggedUser = $this->checkPermissions($company, $deal);

        try {
            return MediaService::uploadChunkMedia(
                MediaType::document,
                $request,
                DealDocument::select('id', 'deal_id', 'company_id', 'user_id', 'type', 'link', 'name')
                    ->findOrFail($request->id),
                config('custom.media_collections.deal_document'),
                $loggedUser,
                [$loggedUser],
                [
                    'action' => ActivityLogAction::uploadedDealDocument,
                    'author_model' => ModelType::userType,
                    'author_id' => $loggedUser->id,
                    'subject_model' => ModelType::deals,
                    'subject_id' => $deal->id,
                    'additional_data' => [
                        'deal_name' => $deal->name,
                        'event' => "Added new document",
                        'description' => $request->has('title') ? 'Document "' . $request->title . '"' : '',
                    ],
                    'ip_address' => $_SERVER["REMOTE_ADDR"],
                ]
            );
        } catch (Exception $e) {
            Log::error(__CLASS__ . '::' . __FUNCTION__ . '::ERROR::' . $e->getMessage());
            DealDocument::where('id', $request->id)->delete();

            throw $e;
        }
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/deals/{company}/deal/{deal}/documents/update",
     *     operationId="deals/documents/updateDocument",
     *     tags={"DealDocumentController"},
     *     summary="Update deal document for a company",
     *     description="Update deal document for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    //</editor-fold>
    // API Call: PUT
    // {{server}}api/v1/deals/{company}/deal/{deal}/documents/update
    /*
    {
        "id":"required|numeric|exists:deal_documents,id|isFromDeal"
        "title":"required|string|profanityCheck"
    }
    */
    // Bearer token needed
    public function updateDocument(
        DealDocumentUpdateRequest $request, Company $company, Deal $deal): DealDocumentResource
    {
        $loggedUser = $this->checkPermissions($company, $deal);
        $dealDocument = DealDocument::select('id', 'deal_id', 'company_id', 'user_id', 'type', 'link', 'name',
            'created_at', 'updated_at')
            ->findOrFail($request->id);
        // Checking if document belongs to company
        if ($dealDocument->company_id !== $company->id) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.DEAL_DOCUMENT_NOT_BELONGS_TO_COMPANY')]
            );
        }
        $newName = $request->name ?? 'empty';
        $oldName = $dealDocument->name ?? 'empty';
        $dealDocument->name = $newName;
        $dealDocument->save();
        $document = $dealDocument->mediaFile;
        if ($document) {
            $customProperties = MediaHelper::updateCustomProperties($request, $document->custom_properties, $loggedUser);
            $document->custom_properties = $customProperties;
            $document->save();
            ActivityLogsService::store(
                ActivityLogAction::updatedDealDocument,
                ModelType::userType,
                $loggedUser->id,
                ModelType::deals,
                $deal->id,
                [
                    'deal_name' => $deal->name,
                    'event' => 'Edited document',
                    'description' => 'From "' . $oldName . '" -> "' . $newName . '"',
                ]
            );
        }

        return new DealDocumentResource($dealDocument);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/deals/{company}/deal/{deal}/documents/delete",
     *     operationId="deals/documents/delete",
     *     tags={"DealDocumentController"},
     *     summary="Delete deal document or link for a company",
     *     description="Delete deal document or link for a company",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: DELETE
    // {{server}}api/v1/deals/{company}/deal/{deal}/documents/delete
    /*
    {
        "id":"required|numeric|exists:deal_documents,id|isFromDeal"
    }
    */
    // Bearer token needed
    public function delete(DealDocumentDeleteRequest $request, Company $company, Deal $deal): JsonResponse
    {
        $this->checkPermissions($company, $deal);
        $dealDocument = DealDocument::select('id', 'deal_id', 'company_id', 'user_id', 'type', 'link', 'name')
            ->where('id', $request->id)
            ->with(['mediaFile'])
            ->first();
        // Checking if document belongs to company
        if ($dealDocument->company_id !== $company->id) {
            throw ValidationException::withMessages(
                ['message' => config('genericMessages.error.DEAL_DOCUMENT_NOT_BELONGS_TO_COMPANY')]
            );
        }
        $dealDocument->delete();
        ActivityLogsService::store(
            ActivityLogAction::deletedDealDocument,
            ModelType::userType,
            AuthService::getLoggedInUserId(),
            ModelType::deals,
            $deal->id,
            [
                'deal_name' => $deal->name,
                'event' => 'Deleted document',
                'description' => 'Deleted document ' . $dealDocument->name,
            ]
        );

        return response()->json();
    }
}
