<?php

namespace App\Http\Controllers\Api\Question;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Question\QuestionFormShowAllRequest;
use App\Http\Resources\Question\Form\QuestionFormResource;
use App\Models\Question\QuestionForm;

class QuestionFormController extends Controller
{
    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/question/form",
     *     operationId="question/questions/form",
     *     tags={"QuestionFormController"},
     *     summary="Get form for a particular form type",
     *     description="Get form for a particular form type",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     */
    //</editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/question/form
    // PARAMS
    /*
       ?form_type="required|string|allowedValues"
     *
     * */
    //needs Bearer Token
    public function show(QuestionFormShowAllRequest $request)
    {
        $questions = QuestionForm::where('form_type', $request->form_type)
            ->where('is_archived', false)
            ->first();

        return new QuestionFormResource($questions);
    }
}
