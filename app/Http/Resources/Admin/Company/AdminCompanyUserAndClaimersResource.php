<?php

namespace App\Http\Resources\Admin\Company;

use App\Http\Resources\Admin\User\AdminUserRoleResource;
use App\Models\Permission\Role\Role;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminCompanyUserAndClaimersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $roles = $this->whenLoaded('roles');
        $roles = $roles instanceof Collection ? AdminUserRoleResource::collection($roles) : null;
        $company = $this->whenLoaded('company');
        $companyName = $company instanceof Model ? $company->name : null;

        return [
            'id' => '' . $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at,
            'phone_verified_at' => $this->phone_verified_at,
            'created_at' => $this->created_at,
            'hubspot_contact_id' => $this->hubspot_contact_id,
            'handle' => $this->handle,
            'friendly_url' => $this->friendly_url,
            'user_profile_type_id' => $this->user_profile_type_id,
            'is_private' => $this->is_private,
            'is_profile_complete' => $this->is_profile_complete,
            'user_profile_type_value' => $this->user_profile_type_value,
            'company_name' => $companyName,
            'company_id' => '' . $this->company_id,
            'last_logged_in_at' => $this->last_logged_in_at,
            'status' => $this->status,
            'company_type_label' => $this->company_type_label,
            'registration_step' => $this->registration_step,
            'company_role' => $this->company_role_id ? new AdminUserRoleResource(Role::find($this->company_role_id)) : null,
            'roles' => $roles,
        ];
    }
}
