<?php

namespace App\Http\Resources\Admin\Analytics\Video;

use App\Http\Resources\Category\CategoryResource;
use App\Http\Resources\TagResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VideoAnalyticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'video_name' => $this->video_name,
            'duration' => $this->duration,
            'view_count' => $this->view_count,
            'date_uploaded' => $this->date_uploaded,
            'company_handle' => $this->company_handle,
            'company_friendly_url' => $this->company_friendly_url,
            'user_handle' => $this->user_handle,
            'user_friendly_url' => $this->user_friendly_url,
            'categories' => CategoryResource::collection($this->categories ?? collect()),
            'tags' => TagResource::collection($this->tags ?? collect()),
        ];
    }
}
