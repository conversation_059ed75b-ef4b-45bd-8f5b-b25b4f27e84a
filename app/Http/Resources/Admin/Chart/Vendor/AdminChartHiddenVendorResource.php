<?php

namespace App\Http\Resources\Admin\Chart\Vendor;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminChartHiddenVendorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'company_id' => '' . $this->company_id,
            'company_name' => $this->company_name,
            'hidden_on_all_charts' => $this->hidden_on_all_charts,
            'category_id' => '' . $this->category_id,
            'category_name' => $this->category_name,
            'pitch_event_id' => '' . $this->pitch_event_id,
            'pitch_event_name' => $this->pitch_event_name,
            'is_hidden_for_chart' => $this->is_hidden_for_chart,
        ];
    }
}
