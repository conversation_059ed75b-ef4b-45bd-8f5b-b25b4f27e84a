<?php

namespace App\Http\Resources\Admin\Review;

use App\Http\Resources\Review\ReviewUserResource;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $this->model->model_type = $this->model_type;

        return [
            'id' => '' . $this->id,
            'read' => $this->read,
            'product_user_verified' => $this->product_user_verified,
            'status' => $this->status,
            'title' => $this->title,
            'incentivize' => $this->incentivize,
            'recommended_by_reviewer_value' => $this->recommended_by_reviewer_value,
            'hide_reviewer_name' => $this->hide_reviewer_name,
            'model' => new AdminReviewModelResource($this->whenLoaded('model')),
            'answers' => AdminReviewAnswerResource::collection($this->whenLoaded('answers')),
            'reviewer_user_id' => $this->reviewer_user_id ? '' . $this->reviewer_user_id : null,
            'reviewer' => new ReviewUserResource($this->whenLoaded('reviewer')),
            'approved_user_id' => $this->approved_user_id ? '' . $this->approved_user_id : null,
            'approved_date' => $this->approved_date,
            'approver' => new UserResource($this->whenLoaded('approver')),
            'under_review_user_id' => $this->under_review_user_id ? '' . $this->under_review_user_id : null,
            'under_reviewer' => new UserResource($this->whenLoaded('underReviewer')),
            'under_review_date' => $this->under_review_date,
            'archived_date' => $this->archived_date,
            'red_flagged' => $this->red_flagged,
            'red_flag_user_id' => $this->red_flag_user_id ? '' . $this->red_flag_user_id : null,
            'red_flag_date' => $this->red_flag_date,
            'red_flag_reason' => $this->red_flag_reason,
            'flagger' => new UserResource($this->whenLoaded('flagger')),
            'reviewer_update_review_at' => $this->reviewer_update_review_at,
            'incentive' => new AdminIncentiveResource($this->whenLoaded('incentive')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'flagged_reviews' => $this->flaggedReview ?? [],
        ];
    }
}
