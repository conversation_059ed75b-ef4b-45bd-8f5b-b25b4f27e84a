<?php

namespace App\Http\Resources\Admin\Review;

use App\Http\Resources\Admin\Review\Tango\TangoCatalogResource;
use App\Http\Resources\UserSearchResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminIncentiveResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'tango_reference_order_id' => '' . $this->tango_reference_order_id,
            'reviewed_by' => '' . $this->reviewed_by,
            'reviewed' => new UserSearchResource($this->whenLoaded('reviewed')),
            'review_id' => '' . $this->review_id,
            'review' => new AdminReviewResource($this->whenLoaded('review')),
            'reviewer_id' => '' . $this->reviewer_id,
            'reviewer_user' => new UserSearchResource($this->whenLoaded('reviewer')),
            'tango_catalog_id' => '' . $this->tango_catalog_id,
            'tango_catalog' => new TangoCatalogResource($this->whenLoaded('tangoCatalog')),
            'status' => $this->status,
            'rejection_reason' => $this->rejection_reason,
            'amount' => $this->amount,
            'paid_at' => $this->paid_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
