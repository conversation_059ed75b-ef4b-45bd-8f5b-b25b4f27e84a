<?php

namespace App\Http\Resources;

use App\Http\Resources\Media\ImageSimpleResource;
use App\Models\Blog\Blog;
use App\Models\Comment\Comment;
use App\Models\IndustryEvent\IndustryEvent;
use App\Models\MediaGallery;
use App\Models\PitchEvent\PitchEvent;
use App\Models\Profile\CompanyProfileType;
use App\Models\ShoutOut\ShoutOut;
use App\Services\MediaService;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class SubjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @throws ValidationException
     */
    public function toArray($request): array
    {
        return match (get_class($this->resource)) {
            Blog::class => $this->buildBlog(),
            Comment::class => $this->buildComment(),
            ShoutOut::class => $this->buildShoutOut(),
            Media::class => $this->buildMedia(),
            MediaGallery::class => $this->buildMediaGallery(),
            IndustryEvent::class => $this->buildIndustryEvent(),
            PitchEvent::class => $this->buildPitchEvent(),
            default => $this->buildCompanyOrUser(),
        };
    }

    /**
     * @throws ValidationException
     */
    private function buildCompanyOrUser(): array
    {
        $avatar = null;
        if (!empty($this->avatar)) {
            if (is_string($this->avatar)) {
                $avatar = $this->avatar;
            } elseif (get_class($this->avatar) === Media::class) {
                $avatar = Storage::temporaryUrlForDisk($this->avatar->getPath(), MediaService::getExpirationTime(), $this->avatar->disk);
            } else {
                throw ValidationException::withMessages([
                    'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for type of avatar::' . $this->avatar,
                ]);
            }
        }
        if (empty($this->name)) {
            $name = $this->first_name . ' ' . $this->last_name;
            $type = 'user';
        } else {
            $name = $this->name;
            $type = 'company';
        }

        if (!empty($this->company_profile_types_id)) {
            $companySubscriptionValue = CompanyProfileType::select('value', 'label')
                ->where('id', $this->company_profile_types_id)->first();
        }

        return [
            'id' => '' . $this->id,
            'type' => $type,
            'name' => $name,
            'description' => $this->description,
            'handle' => $this->profile_vendor_handle ?? $this->handle,
            'friendly_url' => $this->friendly_url,
            'subdomain' => $this->subdomain ?? null,
            'avatar' => $avatar,
            'rating' => $this->rating ?? null,
            'is_private' => !empty($this->is_private) ? $this->is_private : false,
            'subscription' => !empty($this->company_profile_types_id) ? ["value" => $companySubscriptionValue->value, "label" => $companySubscriptionValue->label] : [],
            'type_is_of_vendor' => $this->type_is_of_vendor ?? null,
            'is_distributor' => $this->is_distributor ?? null,
            'manage_clients' => $this->manage_clients ?? null,
            'manage_expenses' => $this->manage_expenses ?? null,
        ];
    }

    private function buildBlog(): array
    {
        $images = !empty($this->images) ? ImageSimpleResource::collection($this->images) : null;
        $blogResource = [
            'id' => '' . $this->id,
            'type' => 'blog',
            'title' => $this->title,
            'friendly_url' => $this->friendly_url,
            'status' => $this->status,
            'media_visibility' => $this->media_visibility,
        ];
        if ($images !== null && count($images)) {
            $blogResource['images'] = $images;
        }

        return $blogResource;
    }

    private function buildComment(): array
    {
        return [
            'id' => '' . $this->id,
            'type' => 'comment',
            'comment' => $this->comment,
            'subject' => new SubjectResource($this->subject),
        ];
    }

    private function buildShoutOut(): array
    {
        return [
            'id' => '' . $this->id,
            'type' => 'shoutOut',
            'shout_out' => $this->shout_out,
        ];
    }

    private function buildMedia(): array
    {
        $thumbnailUrl = $this->thumbnail ? $this->thumbnail->getFullUrl() : null;

        return [
            'id' => '' . $this->id,
            'type' => 'media',
            'model_id' => '' . $this->model_id,
            'name' => $this->name,
            'collection_name' => $this->collection_name,
            'file_name' => $this->file_name,
            'mime_type' => $this->mime_type,
            'custom_properties' => $this->custom_properties,
            'size' => $this->size,
            'url' => Storage::temporaryUrl($this->getPath(), MediaService::getExpirationTime()),
            'thumbnail_url' => $thumbnailUrl,
        ];
    }

    private function buildMediaGallery(): array
    {
        return [
            'id' => '' . $this->id,
            'type' => 'mediaGallery',
            'model_id' => '' . $this->model_id,
            'custom_properties' => $this->custom_properties,
            'medias' => SubjectResource::collection($this->whenLoaded('media')),
        ];
    }

    private function buildIndustryEvent(): array
    {
        return [
            'id' => '' . $this->id,
            'event_name' => $this->event_name,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'subject' => new SubjectResource($this->whenLoaded('subject')),
        ];
    }

    private function buildPitchEvent(): array
    {
        return [
            'id' => '' . $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
        ];
    }
}
