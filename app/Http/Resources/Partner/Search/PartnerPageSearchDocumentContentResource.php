<?php

namespace App\Http\Resources\Partner\Search;

use App\Enums\Partner\PartnerPageSectionLayout;
use App\Http\Resources\DocumentResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PartnerPageSearchDocumentContentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $documents = $this->get(PartnerPageSectionLayout::getKey(PartnerPageSectionLayout::Document)) ?: collect();

        return [
            'documents' => DocumentResource::collection($documents)->response()->getData(true),
        ];
    }
}
