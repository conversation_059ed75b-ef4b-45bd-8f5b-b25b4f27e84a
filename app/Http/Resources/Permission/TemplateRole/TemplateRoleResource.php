<?php

namespace App\Http\Resources\Permission\TemplateRole;

use App\Http\Resources\Company\CompanyTypeResource;
use App\Http\Resources\Permission\Group\PermissionGroupResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TemplateRoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'key' => $this->key,
            'title' => $this->title,
            'display_name' => $this->display_name,
            'description' => $this->description,
            'company_types' => CompanyTypeResource::collection($this->whenLoaded('companyTypes')),
            'permission_groups' => PermissionGroupResource::collection($this->whenLoaded('permissionGroups')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
