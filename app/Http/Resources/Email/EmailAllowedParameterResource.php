<?php

namespace App\Http\Resources\Email;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmailAllowedParameterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'email_id' => $this->email_id ? '' . $this->email_id : null,
            'parameter' => $this->parameter,
            'description' => $this->description,
            'table' => $this->table,
            'column' => $this->column,
            'value' => $this->value,
            'email' => new EmailResource($this->whenLoaded('email')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
