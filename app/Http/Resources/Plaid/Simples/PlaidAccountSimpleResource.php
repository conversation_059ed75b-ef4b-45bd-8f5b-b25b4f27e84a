<?php

namespace App\Http\Resources\Plaid\Simples;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlaidAccountSimpleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'account_id' => $this->account_id,
            "mask" => $this->mask,
            "name" => $this->name,
            "type" => $this->type,
            "institution_logo" => $this->plaidBankLink->institution->logo,
        ];
    }
}
