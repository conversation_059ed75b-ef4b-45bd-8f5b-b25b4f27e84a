<?php

namespace App\Http\Resources\StatusScope;

use App\Http\Resources\JobTitleResource;
use App\Services\MediaService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class StatusScopeAuthorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $avatar = null;
        if ($this->resource->relationloaded('avatar')) {
            $avatar = $this->avatar ?
                Storage::temporaryUrl($this->avatar->getPath(), MediaService::getExpirationTime()) : null;
        }
        $userProfileTypeValue = $this->profile_type_value ?? null;

        return [
            'id' => '' . $this->id,
            'company_id' => '' . $this->company_id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'handle' => $this->handle,
            'status' => $this->status,
            'friendly_url' => $this->friendly_url,
            'job_title_id' => '' . $this->job_title_id,
            'job_title' => new JobTitleResource($this->whenLoaded('jobTitle')),
            'avatar' => $avatar,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'user_profile_type' => $userProfileTypeValue,
        ];
    }
}
