<?php

namespace App\Http\Resources\Comment;

use App\Http\Resources\AuthorResource;
use App\Http\Resources\Company\CompanySimpleResource;
use App\Http\Resources\Media\ImageSimpleResource;
use App\Http\Resources\SubjectResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CommentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'comment' => $this->comment,
            'status' => $this->status,
            'images' => ImageSimpleResource::collection($this->whenLoaded('images')),
            'subject_id' => '' . $this->subject_id,
            'subject_type' => $this->subject_type,
            'subject' => new SubjectResource($this->whenLoaded('subject')),
            'author_id' => '' . $this->author_id,
            'author' => new AuthorResource($this->whenLoaded('author')),
            'author_company_id' => $this->author_company_id ? '' . $this->author_company_id : null,
            'author_company' => new CompanySimpleResource($this->whenLoaded('authorCompany')),
            'flagged_user_id' => $this->flagged_user_id ? '' . $this->flagged_user_id : null,
            'flagged_reason' => $this->flagged_reason,
            'flagged_date' => $this->flagged_date,
            'flagged_by' => new AuthorResource($this->whenLoaded('flaggedBy')),
            'approved_user_id' => $this->approved_user_id ? '' . $this->approved_user_id : null,
            'approved_date' => $this->approved_date,
            'approved_by' => new AuthorResource($this->whenLoaded('approvedBy')),
            'rejected_user_id' => $this->rejected_user_id ? '' . $this->rejected_user_id : null,
            'rejected_reason' => $this->rejected_reason,
            'rejected_date' => $this->rejected_date,
            'rejected_by' => new AuthorResource($this->whenLoaded('rejectedBy')),
            'replies' => CommentReplyResource::collection($this->whenLoaded('descendants')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
