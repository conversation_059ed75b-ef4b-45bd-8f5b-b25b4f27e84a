<?php

namespace App\Http\Resources\Contract;

use Illuminate\Http\Resources\Json\JsonResource;

class ContractKPIsResource extends JsonResource
{
    public function toArray($request): array
    {
        $monthlyCost = ContractCategoryCostKPIResource::collection($this->monthlyCostElements)->values();
        $yearlyCost = ContractCategoryCostKPIResource::collection($this->yearlyCostElements)->values();
        $upcomingRenewals = ContractUpcomingRenewalsResource::collection($this->upcomingRenewals)->values();

        $response = [
            "monthly_cost" => $monthlyCost,
            "yearly_cost" => $yearlyCost,
            "upcoming_renewals" => $upcomingRenewals,
        ];

        return $response;
    }
}
