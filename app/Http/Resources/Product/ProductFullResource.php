<?php

namespace App\Http\Resources\Product;

use App\Http\Resources\Company\CompanySimpleResource;
use App\Http\Resources\Media\ImageSimpleResource;
use App\Http\Resources\Review\ReviewResource;
use App\Http\Resources\VideoResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductFullResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $images = $this->images ? ImageSimpleResource::collection($this->images) : null;
        $videos = $this->videos ? VideoResource::collection($this->videos) : null;
        $rating = $this->rating ?? null;
        $totalReviews = $this->total_reviews ?? null;

        return [
            'id' => '' . $this->id,
            'company_id' => '' . $this->company_id,
            'company' => new CompanySimpleResource($this->whenLoaded('company')),
            'name' => $this->name,
            'description' => $this->description,
            'url' => $this->url,
            'rating' => $rating,
            'total_reviews' => $totalReviews,
            'reviews' => ReviewResource::collection($this->whenLoaded('reviews')),
            'images' => $images,
            'videos' => $videos,
            'friendly_url' => $this->friendly_url,
            'categories' => ProductCategoryResource::collection($this->whenLoaded('categories')),
            'likes_count' => !empty($this->likes_count) ? $this->likes_count : 0,
            'user_has_liked' => !empty($this->user_has_liked) ? $this->user_has_liked : false,
            'features' => ProductFeatureResource::collection($this->features()->get()),
            'pricing' => ProductPricingResource::collection($this->pricing()->get()),
        ];
    }
}
