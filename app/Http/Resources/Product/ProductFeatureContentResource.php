<?php

namespace App\Http\Resources\Product;

use App\Http\Resources\AuthorResource;
use App\Http\Resources\Media\ImageSimpleResource;
use App\Http\Resources\VideoResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductFeatureContentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $images = $this->images ? ImageSimpleResource::collection($this->images) : null;
        $videos = $this->videos ? VideoResource::collection($this->videos) : null;

        return [
            'id' => '' . $this->id,
            'company_id' => '' . $this->company_id,
            'name' => $this->title,
            'description' => $this->description,
            'url' => $this->url,
            'friendly_url' => $this->friendly_url,
            'images' => $images,
            'videos' => $videos,
            'categories' => ProductCategoryResource::collection($this->whenLoaded('categories')),
            'likes_count' => !empty($this->likes_count) ? $this->likes_count : 0,
            'user_has_liked' => !empty($this->user_has_liked) ? $this->user_has_liked : false,
            'product' => empty($this->product) ? null : new ProductSimpleResource($this->product),
            'author' => new AuthorResource($this->whenLoaded('author')),
        ];
    }
}
