<?php

namespace App\Http\Resources;

use App\Enums\ShortenableType;
use App\Http\Resources\Admin\Category\AdminCategoryResource;
use App\Http\Resources\Admin\Tag\AdminTagResource;
use App\Services\MediaService;
use App\Services\ShortenerService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class VideoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $thumbnailUrl = $this->thumbnail ? $this->thumbnail->getFullUrl() : null;
        $shareLink = $this->shareLink ? $this->shareLink->default_short_url :
            ShortenerService::getFEUrlforModelType(ShortenableType::coerce('Video'), $this);
        $viewCounts = $this->views_count ? $this->views_count->total_views : 0;
        $likesCount = !empty($this->likes_count) ? $this->likes_count : 0;
        $userHasLiked = !empty($this->user_has_liked) ? $this->user_has_liked : false;
        $tags = !empty($this->tags) ? $this->tags : collect();
        $categories = !empty($this->categories) ? $this->categories : collect();

        return [
            'id' => '' . $this->id,
            'model_id' => '' . $this->model_id,
            'name' => $this->name,
            'collection_name' => $this->collection_name,
            'file_name' => $this->file_name,
            'mime_type' => $this->mime_type,
            'title' => Arr::get($this->custom_properties, 'title', ''),
            'description' => Arr::get($this->custom_properties, 'description', ''),
            'duration' => Arr::get($this->custom_properties, 'duration', ''),
            'custom_properties' => $this->custom_properties,
            'size' => $this->size,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'url' => Storage::temporaryUrl($this->getPath(), MediaService::getExpirationTime()),
            'thumbnail_url' => $thumbnailUrl,
            'share_link' => $shareLink,
            'views_count' => $viewCounts,
            'likes_count' => $likesCount,
            'user_has_liked' => $userHasLiked,
            'categories' => AdminCategoryResource::collection($categories),
            'tags' => AdminTagResource::collection($tags),
        ];
    }
}
