<?php

namespace App\Http\Resources\Explorer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExplorerVideosPageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $trendingVideos = $this->get('trending_videos') ?: collect();
        $latestVideos = $this->get('latest_videos') ?: collect();
        $topCategoryVideos = $this->get('top_category_videos') ?: collect();
        $topTagVideos = $this->get('top_tag_videos') ?: collect();

        return [
            'trending_videos' => ExplorerLandingPageVideoResource::collection($trendingVideos),
            'latest_videos' => ExplorerLandingPageVideoResource::collection($latestVideos),
            'top_category_videos' => ExplorerGroupedVideosResource::collection($topCategoryVideos),
            'top_tag_videos' => ExplorerGroupedVideosResource::collection($topTagVideos),
        ];
    }
}
