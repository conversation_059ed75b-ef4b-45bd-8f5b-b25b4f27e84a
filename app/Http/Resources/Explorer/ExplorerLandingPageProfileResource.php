<?php

namespace App\Http\Resources\Explorer;

use App\Http\Resources\Filter\UserProfileTypeFilterResource;
use App\Services\MediaService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ExplorerLandingPageProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $avatarUrl = $this->avatar ?
            Storage::temporaryUrlForDisk($this->avatar->getPath(), MediaService::getExpirationTime(), $this->avatar->disk) : null;

        return [
            'id' => '' . $this->id,
            'name' => $this->first_name . ' ' . $this->last_name,
            'handle' => $this->handle,
            'friendly_url' => $this->friendly_url,
            'likes_count' => !empty($this->likes_count) ? $this->likes_count : 0,
            'user_has_liked' => !empty($this->user_has_liked) ? $this->user_has_liked : false,
            'avatar' => $avatarUrl,
            'profile_type' => new UserProfileTypeFilterResource($this->whenLoaded('userProfileType')),
            'status' => $this->status,
            'followers_count' => $this->users_following_me_count,
            'current_user_is_following' => $this->current_user_is_following,
            'job_title_name' => $this->job_title_name,
            'is_private' => $this->is_private,
        ];
    }
}
