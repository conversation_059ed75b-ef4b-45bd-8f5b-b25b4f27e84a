<?php

namespace App\Http\Resources\Follow;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FollowersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'first_name' => '' . $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'handle' => $this->handle,
            'friendly_url' => $this->friendly_url,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
