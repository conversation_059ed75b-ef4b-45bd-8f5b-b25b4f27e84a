<?php

namespace App\Http\Resources\Review;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewQuestionOptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'review_question_id' => '' . $this->review_question_id,
            'key' => $this->key,
            'display_value' => $this->display_value,
            'is_archived' => $this->is_archived,
            'archived_at' => $this->archived_at,
            'order' => $this->order,
            'impact_rating_value' => $this->impact_rating_value,
            'show_answer_option' => $this->show_answer_option,
            'show_answer_option_label' => $this->show_answer_option_label,
            'validation' => $this->validation,
            'question' => new ReviewQuestionResource($this->whenLoaded('question')),
        ];
    }
}
