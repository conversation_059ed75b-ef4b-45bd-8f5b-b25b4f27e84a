<?php

namespace App\Http\Resources\Company;

use App\Http\Resources\Category\CategoryResource;
use App\Services\MediaService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class CompanyTopTrendingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $avatar = $this->avatar ?
            Storage::temporaryUrlForDisk($this->avatar->getPath(), MediaService::getExpirationTime(), $this->avatar->disk) : null;

        return [
            'id' => '' . $this->id,
            'rating' => $this->rating,
            'period' => $this->period,
            'name' => $this->name,
            'friendly_url' => $this->friendly_url,
            'subdomain' => $this->subdomain,
            'avatar' => $avatar,
            'products_categories' => CategoryResource::collection($this->products_categories) ?? null,
            'is_distributor' => $this->is_distributor,
            'manage_clients' => $this->manage_clients,
            'manage_expenses' => $this->manage_expenses,
        ];
    }
}
