<?php

namespace App\Http\Resources\Company;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyClaimerResponseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => '' . $this->id,
            'response' => $this->response,
            'status' => $this->status,
            'referral' => $this->referral,
            'user' => [
                'id' => '' . $this->user->id,
                'email' => $this->user->email,
                'name' => $this->user->first_name . ' ' . $this->user->last_name,
                'friendly_url' => $this->user->friendly_url,
            ],
            'company' => [
                'id' => '' . $this->company->id,
                'name' => $this->company->name,
                'profile_company_website_url' => $this->company->profile_company_website_url,
                'friendly_url' => $this->company->friendly_url,
                'subdomain' => $this->company->subdomain,
            ],
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
