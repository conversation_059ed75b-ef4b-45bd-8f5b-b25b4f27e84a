<?php

namespace App\Http\Resources\Company\Affiliate;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyBrandStackAdoptionDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'category_id' => '' . $this->id,
            'affiliates_using_it_count' => $this->affiliates_using_it_count,
            'affiliates_count' => $this->affiliates_count,
            'affiliates' => CompanyBrandStackAdoptionAffiliateResource::collection($this->affiliates),
            'sub_categories' => CompanyBrandStackAdoptionSubCategoryResource::collection($this->sub_categories),
        ];
    }
}
