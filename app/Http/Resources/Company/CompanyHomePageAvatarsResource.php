<?php

namespace App\Http\Resources\Company;

use App\Services\MediaService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class CompanyHomePageAvatarsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $avatar = $this->avatar ?
            Storage::temporaryUrlForDisk(
                $this->avatar->getPath(), MediaService::getExpirationTime(), $this->avatar->disk) : null;

        return [
            'id' => '' . $this->id,
            'name' => $this->name,
            'avatar' => $avatar,
        ];
    }
}
