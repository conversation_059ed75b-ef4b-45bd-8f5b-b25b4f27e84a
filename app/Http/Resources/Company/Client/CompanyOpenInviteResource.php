<?php

namespace App\Http\Resources\Company\Client;

use App\Http\Resources\Company\CompanySimpleResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyOpenInviteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return
            [
                'parent_company_id' => '' . $this->id,
                'parent_company' => new CompanySimpleResource($this),
            ];
    }
}
