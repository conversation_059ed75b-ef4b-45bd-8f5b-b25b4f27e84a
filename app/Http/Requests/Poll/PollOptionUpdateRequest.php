<?php

namespace App\Http\Requests\Poll;

use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class PollOptionUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => 'required|numeric|exists:poll_options,id',
            'option' => ['sometimes', 'required', 'max:200', new Profanity()],
            'shows_on_reports' => 'sometimes|required|boolean',
            'order' => 'sometimes|required|integer',
        ];
    }
}
