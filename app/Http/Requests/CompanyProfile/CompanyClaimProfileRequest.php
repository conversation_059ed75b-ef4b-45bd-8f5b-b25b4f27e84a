<?php

namespace App\Http\Requests\CompanyProfile;

use App\Models\Company\Company;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class CompanyClaimProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $company = Company::with('companyClaimers')->findOrFail($this->request->get('company_id'));
        $response = Gate::inspect('add-company-profile-claimer', $company);

        if (!$response->allowed()) {
            abort(403, $response->message());
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'company_id' => 'required|numeric|exists:companies,id',
            'user_id' => 'required|numeric|exists:users,id',
            'send_notification_email' => 'required|boolean',
        ];
    }
}
