<?php

namespace App\Http\Requests\Social;

use App\Rules\CustomUrl;
use Illuminate\Foundation\Http\FormRequest;

class SocialSyncRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'socials' => ['array'],
            'socials.*.social_media_id' => 'sometimes|required|numeric|exists:social_media,id',
            'socials.*.url' => ['sometimes', 'required', new CustomUrl()],
        ];
    }
}
