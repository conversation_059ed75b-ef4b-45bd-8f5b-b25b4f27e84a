<?php

namespace App\Http\Requests\Analytics;

use App\Enums\AnalyticAction;
use App\Enums\AnalyticSubjectType;
use App\Rules\AllowedValues;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class AnalyticsStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @throws ValidationException
     */
    public function rules(): array
    {
        $rules = [
            'action' => ['required', 'string', new AllowedValues(AnalyticAction::getKeys())],
            'custom_properties' => 'sometimes|required',
            'subject_id' => 'required|numeric',
        ];

        if (in_array($this->action, [AnalyticAction::like, AnalyticAction::dislike])) {
            $rules['subject_type'] = [new AllowedValues(AnalyticSubjectType::getKeys())];
        } elseif (in_array($this->action, [AnalyticAction::referredRegistration, AnalyticAction::referredReview,
            AnalyticAction::referredReturned, AnalyticAction::referredStack])) {
            $rules = $this->addReferredRules($rules);
        } elseif (in_array($this->action, [AnalyticAction::pageView, AnalyticAction::videoViews])) {
            $rules['custom_properties.user_agent'] = ['required'];
        }

        return $rules;
    }

    /**
     * @throws ValidationException
     */
    private function addReferredRules($rules)
    {
        $rules['custom_properties.referral_id'] = ['required', 'numeric', 'exists:users,id'];
        $subjectTypes = match ($this->action) {
            AnalyticAction::referredReturned, AnalyticAction::referredRegistration => [AnalyticSubjectType::userProfile],
            AnalyticAction::referredReview => [AnalyticSubjectType::product],
            AnalyticAction::referredStack => [AnalyticSubjectType::vendorProfile, AnalyticSubjectType::mspProfile],
            default => throw ValidationException::withMessages([
                'ERROR::' . __CLASS__ . '::' . __FUNCTION__ . '::Logic not implemented for: ' . $this->action,
            ]),
        };
        $rules['subject_type'] = [new AllowedValues($subjectTypes)];

        return $rules;
    }
}
