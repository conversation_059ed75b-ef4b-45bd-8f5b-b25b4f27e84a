<?php

namespace App\Http\Requests;

use App\Enums\SearchModelParams;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Models\Lookup\LookupOption;
use App\Rules\AllowedValues;

class LookupOptionShowAllRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validFilterColumns = $this->getSearchParams(SearchModelParams::LookupSearch);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'lookup_option' => ['required', 'string', new AllowedValues($this->getLookupOptionValues())],
        ]);
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'lookup_option' => $this->route('lookupOption'),
        ]);
    }

    protected function getLookupOptionValues()
    {
        return LookupOption::pluck('name')->toArray();
    }
}
