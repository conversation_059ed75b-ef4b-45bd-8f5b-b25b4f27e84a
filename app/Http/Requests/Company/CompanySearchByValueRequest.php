<?php

namespace App\Http\Requests\Company;

use App\Enums\Company\CompanyRequestUrlOption;
use App\Rules\AllowedValues;
use Illuminate\Foundation\Http\FormRequest;

class CompanySearchByValueRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $allowedValues = ['id', 'likes_count', 'categories', 'revenue', 'address', 'address2', 'city',
            'state', 'zip', 'phone', 'industry', 'employee_range', 'founded',
            'profile_claimer_user_id', 'profile_company_website_url',
            'followers_count', 'features', 'products', 'blogs', 'profile_images',
            'current_user_is_following', 'company_profile_type', 'users', 'rating', 'bulletin',
            'parent', 'parent_id', 'affiliate_brand', 'client_parent', 'claimers',
            'is_affiliate_brand_main_company', 'focuses', 'is_mdf', 'affiliate_id', 'affiliate_brand_id'];

        return [
            'search_by' => ['required', new AllowedValues(CompanyRequestUrlOption::getValues())],
            'value' => 'required|string|min:' . config('common.searchMinForCompany'),
            'requested_fields' => 'sometimes|required|array|min:1',
            'requested_fields.*' => ['required', new AllowedValues($allowedValues)],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'search_by' => strtolower($this->route('search_by')),
            'value' => strtolower($this->route('value')),
        ]);
    }
}
