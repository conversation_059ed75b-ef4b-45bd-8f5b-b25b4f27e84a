<?php

namespace App\Http\Requests\Company\Client;

use App\Enums\Company\CompanyStatus;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;
use App\Rules\NumericArray;
use App\Rules\ValidSearchPartnershipType;
use App\Services\Company\CompanyService;
use Illuminate\Validation\ValidationException;

class CompanyClientShowAllRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = ['name', 'address', 'created_at', 'number_of_endpoints', 'contracts_count', 'partnership_type'];
        $this->dynamicColumnNames = ['address', 'contracts_count', 'partnership_type'];
        $this->columnNamesAliases = ["name" => "companies.name"];
    }
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'number_of_endpoints' => ['sometimes', 'required', 'min:1', new NumericArray()],
            'locations' => ['sometimes', 'required', 'min:1', 'array'],
            'industry' => ['sometimes', 'required', 'min:1', 'array'],
            'vendor_contracts_status' => ['sometimes', 'required', new AllowedValues(CompanyStatus::getKeys())],
            'using_stack' => ['sometimes', 'required', 'numeric', 'exists:my_stack,id'],
            'partnership_type' => ['array'],
            'partnership_type.*' => [
                'string',
                new ValidSearchPartnershipType(request()),
            ],
        ]);
    }

    /**
     * @throws ValidationException
     */
    public function prepareForValidation(): void
    {
        $company = $this->route('company');
        CompanyService::validateCompanyCanManageClients($company);
    }
}
