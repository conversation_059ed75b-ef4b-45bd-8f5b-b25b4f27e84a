<?php

namespace App\Http\Requests\VendorSiteSeo;

use Illuminate\Foundation\Http\FormRequest;

class VendorSiteSeoSocialUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => 'required|numeric',
            'name' => 'string|max:30',
            'url' => 'nullable|string|max:2046',
            'image_url' => 'nullable|string|max:2046',
        ];
    }
}
