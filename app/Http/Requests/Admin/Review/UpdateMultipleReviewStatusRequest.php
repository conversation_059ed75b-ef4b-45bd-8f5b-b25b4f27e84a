<?php

namespace App\Http\Requests\Admin\Review;

use App\Enums\Review\ReviewStatus;
use App\Rules\AllowedValues;
use Illuminate\Foundation\Http\FormRequest;

class UpdateMultipleReviewStatusRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => 'required|array',
            'ids.*' => 'required|numeric|exists:reviews,id',
            'status' => ['required', new AllowedValues(ReviewStatus::getValues('approved', 'flagged', 'underReview'))],
            'reason' => ['required_if:status,' . ReviewStatus::flagged, 'string', 'max:255'],
        ];
    }
}
