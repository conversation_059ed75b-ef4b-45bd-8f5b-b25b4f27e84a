<?php

namespace App\Http\Requests\Admin\Lookup;

use App\Enums\SearchModelParams;
use App\Http\Requests\Admin\AdminSearchRequest;

class LookupOptionShowAllRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validFilterColumns = $this->getSearchParams(SearchModelParams::LookupSearch);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'with_options' => 'sometimes|required|boolean',
            'with_trashed' => 'sometimes|required|boolean',
        ]);
    }
}
