<?php

namespace App\Http\Requests\Admin\Comment;

use App\Enums\CommentStatus;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;

class AdminCommentShowAllRequest extends AdminSearchRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'status' => 'sometimes|required|array|min:1',
            'status.*' => ['required', new AllowedValues(CommentStatus::getKeys())],
        ]);
    }
}
