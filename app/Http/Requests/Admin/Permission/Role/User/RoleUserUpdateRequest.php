<?php

namespace App\Http\Requests\Admin\Permission\Role\User;

use App\Models\Permission\Role\Role;
use App\Rules\NumericArray;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RoleUserUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'as_company_id' => 'required|numeric|exists:companies,id',
            'user_id' => 'required|numeric|exists:users,id',
            'roles' => ['required', new NumericArray(), 'min:1', 'exists:roles,id'],
            'roles.*' => [
                'required',
                'numeric',
                Rule::in(Role::select('id')->where('company_id', app('asCompanyId'))->pluck('id')->toArray()),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'roles.*.in' => config('genericMessages.error.ROLE_NOT_BELONGS_TO_COMPANY'),
        ];
    }
}
