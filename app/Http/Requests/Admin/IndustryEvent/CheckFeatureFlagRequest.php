<?php

namespace App\Http\Requests\Admin\IndustryEvent;

use Illuminate\Foundation\Http\FormRequest;

class CheckFeatureFlagRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => 'sometimes|required|numeric|exists:industry_events,id',
            'subject_id' => 'required|numeric',
            'feature_start_date' => 'required_if:feature_flag,true|date',
            'feature_end_date' => 'required_if:feature_flag,true|after:feature_start_date|date',
            'feature_flag' => ['sometimes', 'required', 'boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'feature_start_date.required_if' => 'The featured start date/time is required',
            'feature_end_date.required_if' => 'The featured end date/time is required',
        ];
    }
}
