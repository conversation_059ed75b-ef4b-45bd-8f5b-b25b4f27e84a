<?php

namespace App\Http\Requests\Admin\UserContent;

use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class AdminUserContentApproveCompanyRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            "name" => ["required", "string", "max:191", new Profanity()],
            "profile_company_website_url" => "required|string|min:2",
            "linkedin" => "sometimes|nullable|string",
            "description" => ["required", "string", "min:2", "max:750", new Profanity()],
        ];
    }
}
