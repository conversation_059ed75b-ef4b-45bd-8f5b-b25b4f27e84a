<?php

namespace App\Http\Requests\Admin\Company\Locations;

use App\Models\Company\Company;
use App\Rules\Profanity;
use App\Rules\UniqueField;
use Illuminate\Foundation\Http\FormRequest;

class AdminLocationStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            "parent_id" => ["required", "numeric", "exists:companies,id"],
            "name" => ["required", "string", "max:191", new Profanity(), new UniqueField(new Company(), "name")],
            "address" => "sometimes|required|string|max:255",
            "emails" => "required_with:template_role_id|array|min:1",
            "emails.*" => "required|email",
            "template_role_id" => ["required_with:emails", "numeric", "exists:template_roles,id"],
            "affiliate_id" => "nullable|string|max:255",
        ];
    }
}
