<?php

namespace App\Http\Requests\Media;

use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class UpdateMediaImageRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['sometimes', 'string', new Profanity()],
            'description' => ['sometimes', 'string', new Profanity()],
        ];
    }
}
