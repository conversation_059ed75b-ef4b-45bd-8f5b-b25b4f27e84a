<?php

namespace App\Http\Requests\Product;

use App\Enums\MediaType;
use App\Rules\MimeTypes;
use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class ProductUploadImageRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'image' => ['required', 'image', 'max:5000', new MimeTypes(MediaType::image)],
            'title' => ['sometimes', 'required', 'string', 'max:100', new Profanity()],
            'description' => ['sometimes', 'required', 'string', 'max:200', new Profanity()],
        ];
    }
}
