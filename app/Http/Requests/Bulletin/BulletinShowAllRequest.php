<?php

namespace App\Http\Requests\Bulletin;

use App\Enums\BulletinStatus;
use App\Enums\PrmMediaVisibility;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;

class BulletinShowAllRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = ["media_visibility", "title", "duration", "status", "start_date",
            "author_name"];
        $this->columnNamesAliases = [
            "author_name" => "users.first_name",
            "media_visibility" => "bulletins.media_visibility",
            "title" => "bulletins.title",
            "duration" => "bulletins.duration",
            "status" => "bulletins.status",
            "start_date" => "bulletins.start_date",
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            "duration" => ["sometimes", "required"],
            "start_date" => ["sometimes", "array", "required"],
            "media_visibility" => ["sometimes", "array", "min:1"],
            "media_visibility.*" => ["required", "string", new AllowedValues(PrmMediaVisibility::getValues())],
            "author_id" => ["sometimes", "array", "min:1"],
            "author_id.*" => ["required", "numeric", "exists:users,id"],
            "status" => ["sometimes", "required", new AllowedValues(BulletinStatus::getKeys())],
        ]);
    }
}
