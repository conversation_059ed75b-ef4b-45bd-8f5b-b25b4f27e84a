<?php

namespace App\Http\Requests\Contract\Document;

use App\Enums\MediaType;
use App\Models\Contract\ContractDocument;
use App\Rules\MimeTypes;
use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContractDocumentUploadRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'numeric',
                'exists:contract_documents,id',
                Rule::in(
                    ContractDocument::where(
                        'contract_id', $this->contract && is_numeric($this->contract->id)
                        ? $this->contract->id
                        : 0
                    )->pluck('id')->toArray()
                ),
            ],
            'document' => 'required|file',
            'title' => ['sometimes', 'required', 'string', new Profanity()],
            'file_extension' => ['sometimes', 'required', new MimeTypes(MediaType::chunkDocument, false)],
        ];
    }

    public function prepareForValidation(): void
    {
        $this->contract = $this->route('contract');
    }

    public function messages(): array
    {
        return [
            'id.in' => 'The selected document does not belong to the specified contract.',
        ];
    }
}
