<?php

namespace App\Http\Requests\Contract\Document;

use App\Rules\CustomUrl;
use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class ContractLinkStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'link' => ['required', 'string', 'max:2048', new CustomUrl()],
            'name' => ['nullable', 'string', new Profanity()],
        ];
    }
}
