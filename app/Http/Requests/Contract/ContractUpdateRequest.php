<?php

namespace App\Http\Requests\Contract;

use App\Models\Company\Company;
use App\Models\Contract\Contract;
use App\Rules\BooleanRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContractUpdateRequest extends FormRequest
{
    private Company $company;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'numeric',
                'exists:contracts,id',
                Rule::in(Contract::where('owner_id', $this->company->id)->pluck('id')->toArray()),
            ],
            'name' => 'sometimes|required|string',
            "currency_id" => "nullable|numeric|exists:currencies,id",
            "has_fixed_rate" => ["sometimes", new BooleanRule()],
            "exchange_rate" => "sometimes|required|numeric|min:0",
            'cost' => 'nullable|numeric|min:0',
            'custom_properties' => 'nullable',
        ];
    }

    public function prepareForValidation(): void
    {
        $this->company = $this->route('company');
    }

    public function messages(): array
    {
        return [
            'in' => config('genericMessages.error.CONTRACT_NOT_BELONGS_TO_COMPANY'),
        ];
    }
}
