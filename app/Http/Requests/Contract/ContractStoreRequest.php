<?php

namespace App\Http\Requests\Contract;

use App\Rules\BooleanRule;
use App\Rules\Profanity;

class ContractStoreRequest extends ProductContractBasicRequest
{
    protected string $prefix = "product_contracts.*.";

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        // Extends basic rules for product contracts
        return array_merge(parent::rules(), [
            "name" => ["sometimes", "required", "string", new Profanity()],
            "parent_id" => "nullable|numeric|exists:contracts,id",
            "company_id" => "required_without:client_vendor_name|numeric|exists:companies,id",
            "client_vendor_name" => ["required_without:company_id", "string", "max:255", new Profanity()],
            "sold_by_distributor" => "required_with:client_vendor_name|boolean",
            "currency_id" => "nullable|numeric|exists:currencies,id",
            "has_fixed_rate" => ["sometimes", new BooleanRule()],
            "exchange_rate" => "sometimes|required|numeric|min:0",
            "cost" => "nullable|numeric|min:0",
            "custom_properties" => "nullable",
            "product_contracts" => "nullable|array|min:1",
        ]);
    }
}
