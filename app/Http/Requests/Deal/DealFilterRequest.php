<?php

namespace App\Http\Requests\Deal;

use App\Enums\Deals\DealStatusEnum;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;
use App\Rules\DateRange;

class DealFilterRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = ['name', 'status', 'date', 'company', 'owner', 'submitter', 'size',
            'product_name', 'attachments_count'];
        $this->columnNamesAliases = [
            'owner' => 'owner.name',
            'company' => 'company.name',
            'name' => 'deals.name',
            'date' => 'submitted_at',
            'status' => 'deals.status',
            'product_name' => 'product.name',
            'attachments_count' => 'count(deal_documents.id)',
            'size' => 'size',
        ];
        $this->dynamicColumnNames = ['company', 'owner', 'submitter', 'status', 'product_name', 'attachments_count'];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'date' => ['sometimes', new DateRange()],
            'deals' => ['sometimes', 'array'],
            'deals.*' => ['required_with:deals', 'numeric', 'exists:deals,id'],
            'submitters' => ['sometimes', 'array'],
            'submitters.*' => ['required_with:submitters', 'numeric', 'exists:users,id'],
            'companies' => ['sometimes', 'array'],
            'companies.*' => ['required_with:companies', 'numeric', 'exists:companies,id'],
            'owners' => ['sometimes', 'array'],
            'owners.*' => ['required_with:owners', 'numeric', 'exists:companies,id'],
            'statuses' => ['sometimes', 'array'],
            'statuses.*' => ['required_with:statuses', 'string', new AllowedValues(DealStatusEnum::getKeys())],
        ]);
    }
}
