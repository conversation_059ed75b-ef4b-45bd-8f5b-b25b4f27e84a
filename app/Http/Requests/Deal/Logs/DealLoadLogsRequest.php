<?php

namespace App\Http\Requests\Deal\Logs;

use App\Enums\ActivityLogAction;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;

class DealLoadLogsRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = ['created_at', 'action', 'first_name'];
        $this->columnNamesAliases = [
            'author.first_name' => 'first_name',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $allowedValues = [
            ActivityLogAction::created,
            ActivityLogAction::updated,
            ActivityLogAction::deleted,
            ActivityLogAction::addedDealStakeholder,
            ActivityLogAction::deletedDealStakeholder,
            ActivityLogAction::uploadedDealDocument,
            ActivityLogAction::deletedDealDocument,
            ActivityLogAction::updatedDealDocument,
        ];

        return array_merge(parent::rules(), [
            'search_word' => 'sometimes|string|min:2',
            'start_date' => 'sometimes|required|date',
            'end_date' => 'sometimes|required|date',
            'event_types' => ['sometimes', 'array', 'min:1'],
            'event_types.*' => ['required', 'string', new AllowedValues($allowedValues)],
            'authors' => ['sometimes', 'array', 'min:1'],
        ]);
    }
}
