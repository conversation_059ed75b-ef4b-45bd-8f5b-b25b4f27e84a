<?php

namespace App\Http\Requests\CpJobs;

use App\Models\CpJobs;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CpJobsStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'key' => ['required', 'max:255', Rule::unique(CpJobs::class)],
            'label' => ['max:255'],
            'name_space' => ['max:255'],
            'cron' => ['max:255'],
        ];
    }
}
