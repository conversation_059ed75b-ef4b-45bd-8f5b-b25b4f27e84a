<?php

namespace App\Http\Requests\PitchEvent;

use App\Enums\MediaType;
use App\Rules\MimeTypes;
use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class PitchEventStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'max:100', new Profanity()],
            'description' => ['required', 'max:1000', new Profanity()],
            'start_date' => 'required|date',
            'third_party_id' => 'string|max:100',
            'hubspot_internal_id' => 'required|unique:pitch_events|max:25',
            'banner_image' => ['sometimes', 'required', 'file', 'image', new MimeTypes(MediaType::image)],
            'addevent_id' => 'nullable|string',
        ];
    }
}
