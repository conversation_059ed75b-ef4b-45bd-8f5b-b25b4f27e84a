<?php

namespace App\Http\Requests\PitchEvent;

use App\Enums\PusherEventType;
use App\Rules\AllowedValues;
use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class PitchDayMsgToModRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'pitch_event_id' => 'required|numeric',
            'message' => ['required', new Profanity()],
            'pusher_channel' => 'required',
            'event' => ['required', new AllowedValues(PusherEventType::getValues())],
        ];
    }
}
