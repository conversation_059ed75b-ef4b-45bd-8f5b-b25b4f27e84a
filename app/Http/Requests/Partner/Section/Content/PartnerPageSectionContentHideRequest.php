<?php

namespace App\Http\Requests\Partner\Section\Content;

use Illuminate\Foundation\Http\FormRequest;

class PartnerPageSectionContentHideRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'partner_page_section_id' => [
                'required',
                'integer',
                'exists:partner_page_sections,id,partner_page_id,' . $this->route('partnerPage')->id,
            ],
            'id' => ['required', 'integer', 'exists:partner_page_section_contents,id,partner_page_section_id,' . $this->partner_page_section_id],
        ];
    }

    public function messages(): array
    {
        return [
            'partner_page_section_id.exists' => config('genericMessages.error.COMPANY_NOT_OWNS_SECTION'),
            'id.exists' => config('genericMessages.error.SECTION_NOT_OWNS_CONTENT'),
        ];
    }
}
