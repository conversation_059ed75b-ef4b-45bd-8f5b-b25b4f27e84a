<?php

namespace App\Http\Requests\Partner\Invites;

use App\Enums\Partner\InviteSortingOptions;
use App\Enums\Partner\PartnerApplicationSource;
use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Enums\SearchModelParams;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;

class GetInvitePartnerRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validFilterColumns = $this->getSearchParams(SearchModelParams::PartnerInviteSearch);
        $this->validOrderByColumnNames = InviteSortingOptions::getValues();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'search.status' => ['array', new AllowedValues(PartnerPortalInvitationStatus::getValues())],
            'search.source' => ['array', new AllowedValues(PartnerApplicationSource::getValues())],
        ]);
    }
}
