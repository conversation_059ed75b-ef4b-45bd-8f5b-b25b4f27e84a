<?php

namespace App\Http\Requests\Partner;

use App\Enums\SearchModelParams;
use App\Http\Requests\Admin\AdminSearchRequest;

class PartnersMSPRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validFilterColumns = $this->getSearchParams(SearchModelParams::PartnerPageSectionContentSearch);
        $this->validOrderByColumnNames = ['id', 'partner_page_section_id', 'subject_id', 'subject_type', 'author_id', 'order', 'created_at', 'updated_at'];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return parent::rules();
    }
}
