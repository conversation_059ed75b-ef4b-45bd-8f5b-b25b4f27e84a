<?php

namespace App\Http\Requests\Partner;

use App\Enums\MyTechnologyStackOrder;
use App\Enums\PrmMediaVisibility;
use App\Enums\SearchModelParams;
use App\Http\Requests\Admin\AdminSearchRequest;
use App\Rules\AllowedValues;

class CompanyPartnerRequest extends AdminSearchRequest
{
    public function __construct()
    {
        $this->validOrderByColumnNames = MyTechnologyStackOrder::getValues();
        $this->validFilterColumns = $this->getSearchParams(array_merge(['accepted_at' => 'between'],
            SearchModelParams::CompanySearch));
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'company_type' => ['sometimes', 'required', new AllowedValues([PrmMediaVisibility::MSP,
                    PrmMediaVisibility::VENDOR, ]), ],
                'accepted_reason' => ['sometimes', 'required', 'array'],
                'accepted_reason.*' => ['exists:lookup_option_values,id'],
            ]
        );
    }
}
