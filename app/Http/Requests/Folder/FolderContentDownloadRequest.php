<?php

namespace App\Http\Requests\Folder;

use App\Models\Folder\CompanyFolder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FolderContentDownloadRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'folder_id' => [
                'required',
                'numeric',
                'exists:company_folders,id',
                Rule::in(CompanyFolder::where('subject_id', $this->company->id)->pluck('id')->toArray()),
            ],
        ];
    }

    public function prepareForValidation(): void
    {
        $this->company = $this->route('company');
    }

    public function messages(): array
    {
        return [
            'folder_id.in' => 'The selected folder does not belong to the specified company.',
            'content_id.in' => 'The selected content does not belong to the specified folder.',
        ];
    }
}
