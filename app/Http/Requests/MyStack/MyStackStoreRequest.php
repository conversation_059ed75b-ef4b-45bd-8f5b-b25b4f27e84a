<?php

namespace App\Http\Requests\MyStack;

use App\Enums\MyStackPartnerStatus;
use App\Rules\AllowedValues;
use App\Rules\NumericKeyRule;
use Illuminate\Foundation\Http\FormRequest;

class MyStackStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'stack' => ['required', 'array', new NumericKeyRule()],
            'stack.*' => 'required|array',
            'stack.*.*.sold_by_distributor' => 'sometimes|required|boolean',
            'stack.*.*.is_recommended_stack' => 'sometimes|required|boolean',
            'stack.*.*.company_id' => 'required|numeric|exists:companies,id',
            'stack.*.*.distributor_id' => 'sometimes|required|numeric|exists:companies,id',
            'stack.*.*.product_id' => 'nullable|numeric|exists:products,id',
            'stack.*.*.partner_status' => ['nullable', 'string', new AllowedValues(MyStackPartnerStatus::getValues())],
            'stack.*.*.client_ids' => 'sometimes|array|min:1',
            'stack.*.*.client_ids.*' => 'required_with:stack.*.client_ids|numeric|exists:companies,id',
        ];
    }

    protected function prepareForValidation()
    {
        if (is_array($this->get('stack'))) {
            $this->merge([
                'categories' => array_keys($this->get('stack')),
            ]);
            $this->merge([
                'company_product' => array_reduce($this->get('stack'), function ($carry, $item) {
                    return array_merge($carry, $item);
                }, []),
            ]);
        }
    }

    // messages
    public function messages(): array
    {
        return [
            'categories.numeric' => 'The stack.categories field must be a number.',
            'categories.required' => 'The stack.categories field is required.',
            'categories.exists' => 'The selected stack.categories is invalid.',
            'categories.*.company_id.numeric' => 'The stack.categories.company_id field must be numeric.',
            'categories.*.company_id.required' => 'The stack.categories.company_id field is required.',
            'categories.*.company_id.exists' => 'The selected stack.categories.company_id is invalid.',
            'categories.*.product_id.required' => 'The stack.categories.product_id field is required.',
            'categories.*.product_id.exists' => 'The selected stack.categories.product_id is invalid.',
            'categories.*.partner_status.required' => 'The stack.categories.partner_status field is required.',
        ];
    }
}
