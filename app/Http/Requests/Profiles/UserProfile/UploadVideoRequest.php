<?php

namespace App\Http\Requests\Profiles\UserProfile;

use App\Enums\MediaType;
use App\Enums\PrmMediaVisibility;
use App\Rules\AllowedValues;
use App\Rules\CantHaveMainCategories;
use App\Rules\MimeTypes;
use App\Rules\NumericArray;
use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class UploadVideoRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'video' => 'required|file',
            'title' => ['sometimes', 'string', new Profanity()],
            'description' => ['sometimes', 'string', new Profanity()],
            'duration' => 'sometimes|required|numeric',
            'thumbnail' => ['sometimes', 'required', 'image', 'max:5000', new MimeTypes(MediaType::image)],
            'categories' => [
                'bail', 'sometimes', 'required',
                new NumericArray(), 'exists:categories,id', new CantHaveMainCategories(),
            ],
            'tags' => ['bail', 'sometimes', 'required', new NumericArray(), 'exists:tags,id'],
            'is_partner_content' => 'sometimes|required|boolean',
            'media_visibility' => ['sometimes', 'required', new AllowedValues(PrmMediaVisibility::getValues())],
            'partner_page_id' => [
                'required_if:is_partner_content,true',
                'integer',
                'exists:partner_pages,id,company_id,' . $this->route('companyId')?->id,
            ],
            'partner_page_section_id' => [
                'required_if:is_partner_content,true',
                'integer',
                'exists:partner_page_sections,id,partner_page_id,' . $this->partner_page_id,
            ],
            'file_extension' => ['sometimes', 'required', new MimeTypes(MediaType::chunkVideo, false)],
        ];
    }

    public function messages(): array
    {
        return [
            'partner_page_id.exists' => config('genericMessages.error.DOESNT_EXISTS'),
            'partner_page_section_id.exists' => config('genericMessages.error.COMPANY_NOT_OWNS_SECTION'),
        ];
    }
}
