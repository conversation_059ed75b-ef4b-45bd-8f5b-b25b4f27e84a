<?php

namespace App\Http\Requests\Profiles\UserProfile;

use App\Enums\MediaType;
use App\Enums\PrmMediaVisibility;
use App\Rules\AllowedValues;
use App\Rules\MimeTypes;
use App\Rules\NumericArray;
use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class UpdateDocumentRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['sometimes', 'string', new Profanity()],
            'description' => ['sometimes', 'string', new Profanity()],
            'is_partner_content' => 'sometimes|required|boolean',
            'thumbnail' => ['sometimes', 'required', 'image', 'max:5000', new MimeTypes(MediaType::image)],
            'tags' => ['sometimes', 'array', new NumericArray(), 'exists:tags,id'],
            'media_visibility' => ['sometimes', 'required', new AllowedValues(PrmMediaVisibility::getValues())],
        ];
    }
}
