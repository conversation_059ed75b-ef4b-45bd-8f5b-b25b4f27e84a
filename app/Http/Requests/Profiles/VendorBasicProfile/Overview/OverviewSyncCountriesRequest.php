<?php

namespace App\Http\Requests\Profiles\VendorBasicProfile\Overview;

use Illuminate\Foundation\Http\FormRequest;

class OverviewSyncCountriesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'countries_supported' => 'nullable|array',
            'countries_supported.*' => ['required_with:countries_supported', 'numeric', 'exists:countries,id'],
        ];
    }
}
