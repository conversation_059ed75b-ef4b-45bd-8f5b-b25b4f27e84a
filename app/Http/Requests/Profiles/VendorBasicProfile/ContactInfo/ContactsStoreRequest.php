<?php

namespace App\Http\Requests\Profiles\VendorBasicProfile\ContactInfo;

use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class ContactsStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255', new Profanity()],
            'email' => ['nullable', 'string', 'max:255'],
            'phone' => 'nullable|string|max:25',
        ];
    }
}
