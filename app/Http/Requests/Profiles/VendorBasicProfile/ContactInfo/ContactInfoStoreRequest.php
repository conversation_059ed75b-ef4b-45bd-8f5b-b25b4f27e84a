<?php

namespace App\Http\Requests\Profiles\VendorBasicProfile\ContactInfo;

use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class ContactInfoStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'manager_name' => ['nullable', 'string', 'max:255', new Profanity()],
            'manager_email' => ['nullable', 'string', 'max:100'],
            'manager_phone' => 'nullable|string|max:25',
            'manager_handle' => ['nullable', 'string', 'max:50', new Profanity()],
            'sales_email' => 'nullable|string|max:100',
            'sales_phone' => 'nullable|string|max:25',
            'support_email' => 'nullable|string|max:100',
            'support_phone' => 'nullable|string|max:25',
        ];
    }
}
