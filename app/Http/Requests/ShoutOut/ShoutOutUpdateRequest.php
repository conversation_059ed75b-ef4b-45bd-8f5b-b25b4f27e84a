<?php

namespace App\Http\Requests\ShoutOut;

use App\Rules\Profanity;
use Illuminate\Foundation\Http\FormRequest;

class ShoutOutUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => 'required|numeric|exists:shout_outs,id',
            'shout_out' => ['required', 'string', 'max:280', new Profanity()],
        ];
    }
}
