<?php

namespace App\Http\Requests\Filter;

use Illuminate\Foundation\Http\FormRequest;

class FilterIndustryEventsPresentersRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'search_word' => 'sometimes|required|string|min:' . config('common.searchMinForCompany'),
        ];
    }
}
