<?php

namespace App\Http\Requests\Explorer;

use App\Enums\ExplorerProfileFilter;
use App\Rules\AllowedValues;
use Illuminate\Foundation\Http\FormRequest;

class ExplorerProfilesRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'result_types' => ['sometimes', 'array', 'min:1'],
            'result_types.*' => ['sometimes', new AllowedValues(ExplorerProfileFilter::getKeys())],
            'vendor_items' => 'sometimes|required|numeric|min:1',
            'influencer_items' => 'sometimes|required|numeric|min:1',
            'user_items' => 'sometimes|required|numeric|min:1',
        ];
    }
}
