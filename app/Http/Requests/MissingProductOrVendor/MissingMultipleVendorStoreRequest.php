<?php

namespace App\Http\Requests\MissingProductOrVendor;

use Illuminate\Foundation\Http\FormRequest;

class MissingMultipleVendorStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'vendor_names' => 'array|min:1',
            'vendor_names.*' => 'string|max:255',
        ];
    }
}
