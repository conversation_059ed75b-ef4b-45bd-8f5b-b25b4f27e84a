<?php

namespace App\Models\Contract;

use App\Models\Category\Category;
use App\Models\Company\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClientProduct extends Model
{
    use HasFactory;
    protected $table = "client_products";
    public $timestamps = true;
    public $fillable = ["name", "description", "category_id", "owner_id"];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(Company::class, "owner_id", "id");
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, "category_id", "id");
    }
}
