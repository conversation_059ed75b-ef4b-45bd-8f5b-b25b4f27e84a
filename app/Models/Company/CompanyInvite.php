<?php

namespace App\Models\Company;

use App\Models\Permission\Role\Role;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompanyInvite extends Model
{
    use HasFactory;

    public $fillable = ['parent_company_id', 'child_company_id', 'email',
        'activated', 'activated_at', 'type', 'author_id', 'user_invite', 'role_id', 'last_sent_at'];

    protected function casts(): array
    {
        return [
            'activated' => 'boolean', 'user_invite' => 'boolean', 'activated_at' => 'datetime', 'last_sent_at' => 'datetime',
        ];
    }

    public function parentCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'parent_company_id', 'id');
    }

    public function invitedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }

    public function childCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'child_company_id', 'id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id', 'id');
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }
}
