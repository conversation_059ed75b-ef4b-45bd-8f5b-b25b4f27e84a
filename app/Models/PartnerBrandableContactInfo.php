<?php

namespace App\Models;

use App\Enums\SearchModelParams;
use App\Models\Company\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PartnerBrandableContactInfo extends Model
{
    protected $guarded = null;

    protected $fillable = ['company_id', 'author_id', 'content', 'title'];

    public $searchable = SearchModelParams::PartnerBrandableContactInfoSearch;

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }
}
