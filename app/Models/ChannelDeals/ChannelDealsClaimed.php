<?php

namespace App\Models\ChannelDeals;

use App\Enums\Lookup\LookupOptionsEnum;
use App\Enums\Lookup\Values\ChannelDealsClaimedValueEnum;
use App\Models\BaseModel;
use App\Models\Company\Company;
use App\Models\Lookup\LookupOptionValue;
use App\Models\User;
use App\Services\LookupService\LookupService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChannelDealsClaimed extends BaseModel
{
    use HasFactory;

    public $timestamps = true;

    protected $table = 'channel_deals_claimed';

    protected $fillable = [
        'request_by_user_email',
        'deal_id',
        'company_id',
        'status_id',
        'reject_reason',
        'reviewer_user_id',
    ];

    protected function casts(): array
    {
        return [
            'id' => 'string',
            'user_id' => 'string',
            'deal_id' => 'string',
            'status_id' => 'string',
            'company_id' => 'string',
            'reviewer_user_id' => 'string',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'request_by_user_email', 'email');
    }

    public function reviewerByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewer_user_id');
    }
    public function deal(): BelongsTo
    {
        return $this->belongsTo(ChannelDeal::class, 'deal_id');
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(LookupOptionValue::class, 'status_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function scopeApproved(Builder $query): Builder
    {
        return $query->where(
            'status_id',
            LookupService::getLookupOptionValueId(LookupOptionsEnum::CHANNEL_DEALS_CLAIMED, ChannelDealsClaimedValueEnum::APPROVED)
        );
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where(
            'status_id',
            LookupService::getLookupOptionValueId(LookupOptionsEnum::CHANNEL_DEALS_CLAIMED, ChannelDealsClaimedValueEnum::PENDING)
        );
    }

    public function scopeDeclined(Builder $query): Builder
    {
        return $query->where(
            'status_id',
            LookupService::getLookupOptionValueId(LookupOptionsEnum::CHANNEL_DEALS_CLAIMED, ChannelDealsClaimedValueEnum::DECLINED)
        );
    }

    public function scopeNotDeclined(Builder $query): Builder
    {
        return $query->whereNot(
            'status_id',
            LookupService::getLookupOptionValueId(LookupOptionsEnum::CHANNEL_DEALS_CLAIMED, ChannelDealsClaimedValueEnum::DECLINED)
        );
    }

    public function scopeJoinCompany(Builder $query): Builder
    {
        return $query->join('companies','companies.id', '=', 'channel_deals_claimed.company_id');
    }

    public function scopeJoinStatus(Builder $query): Builder
    {
        return $query->join('lookup_option_values','lookup_option_values.id', '=', 'channel_deals_claimed.status_id');
    }

    public function scopeJoinDeal(Builder $query): Builder
    {
        return $query->join('channel_deals','channel_deals.id', '=', 'channel_deals_claimed.deal_id');
    }
}
