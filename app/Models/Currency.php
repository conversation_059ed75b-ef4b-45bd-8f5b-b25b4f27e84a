<?php

namespace App\Models;

use App\Models\Contract\Contract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Currency extends Model
{
    use HasFactory;

    protected $table = "currencies";

    public $timestamps = true;

    public $fillable = [
        "key", "name", "description", "order", "symbol",
    ];

    public function setKeyAttribute($value)
    {
        $this->attributes['key'] = strtoupper($value);
    }

    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class, "currency_id", "id");
    }

    public function exchangeRates(): HasMany
    {
        return $this->hasMany(CurrencyExchangeRate::class, "base", "key");
    }
}
