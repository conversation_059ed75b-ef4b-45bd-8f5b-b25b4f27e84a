<?php

namespace App\Models\Permission\Feature;

use App\Models\Company\CompanyType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class CompanyTypePermissionFeatureGroupFeature extends Pivot
{
    use HasFactory;

    public $fillable = ["company_type_id", "permission_feature_group_id", "permission_feature_id"];
    public $table = "company_type_permission_feature_group_features";

    public function companyType(): BelongsTo
    {
        return $this->belongsTo(CompanyType::class, "company_type_id", "id");
    }

    public function permissionFeatureGroup(): BelongsTo
    {
        return $this->belongsTo(PermissionFeatureGroup::class, "permission_feature_group_id", "id");
    }

    public function permissionFeature(): BelongsTo
    {
        return $this->belongsTo(PermissionFeature::class, "permission_feature_id", "id");
    }
}
