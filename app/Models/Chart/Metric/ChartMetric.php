<?php

namespace App\Models\Chart\Metric;

use App\Models\Chart\PitchPoll\ChartPitchPollQuestion;
use App\Models\Chart\PitchPoll\ChartPitchPollQuestionOption;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ChartMetric extends Model
{
    use HasFactory;

    protected $table = 'chart_metrics';

    public $timestamps = true;

    public $fillable = ['name', 'description', 'key'];

    public function chartPitchPollQuestion(): HasOne
    {
        return $this->hasOne(ChartPitchPollQuestion::class, 'chart_metric_id', 'id');
    }

    public function chartPitchPollQuestionsOptions(): HasManyThrough
    {
        return $this->hasManyThrough(
            ChartPitchPollQuestionOption::class,
            ChartPitchPollQuestion::class,
            'chart_metric_id',
            'chart_pitch_poll_question_id',
            'id',
            'id'
        );
    }
}
