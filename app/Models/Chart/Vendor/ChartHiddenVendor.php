<?php

namespace App\Models\Chart\Vendor;

use App\Models\Company\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChartHiddenVendor extends Model
{
    use HasFactory;

    protected $table = 'chart_hidden_vendors';

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
