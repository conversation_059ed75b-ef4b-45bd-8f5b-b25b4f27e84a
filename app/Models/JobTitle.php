<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class JobTitle extends BaseModel
{
    use HasFactory;

    public $timestamps = true;

    protected $fillable = ['name'];

    protected $table = 'job_titles';

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'user_id', 'id');
    }
}
