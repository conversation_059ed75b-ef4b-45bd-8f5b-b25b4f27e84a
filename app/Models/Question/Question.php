<?php

namespace App\Models\Question;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Question extends Model
{
    use HasFactory;

    public $timestamps = true;

    public $fillable = ['id',
        'question_form_type',
        'question',
        'question_type',
        'is_required',
        'is_archived',
        'question_order',
        'archived_at',
        'question_key',
    ];

    protected function casts(): array
    {
        return [
            'is_required' => 'boolean',
            'is_archived' => 'boolean',
            'archived_at' => 'datetime',
        ];
    }

    public function forms(): Has<PERSON>any
    {
        return $this->hasMany(QuestionForm::class, 'form_type', 'question_form_type');
    }

    public function answers(): HasMany
    {
        return $this->hasMany(QuestionAnswer::class, 'question_id', 'id');
    }

    public function allOptions(): HasMany
    {
        return $this->hasMany(QuestionOption::class, 'question_id', 'id')
            ->orderBy('option_order');
    }

    public function archivedOptions(): HasMany
    {
        return $this->hasMany(QuestionOption::class, 'question_id', 'id')
            ->where('is_archived', true)
            ->orderBy('option_order');
    }

    public function activeOptions(): HasMany
    {
        return $this->hasMany(QuestionOption::class, 'question_id', 'id')
            ->where('is_archived', false)
            ->orderBy('option_order');
    }
}
