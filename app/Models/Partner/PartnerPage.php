<?php

namespace App\Models\Partner;

use App\Models\Company\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PartnerPage extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $table = 'partner_pages';

    public $timestamps = true;

    public $fillable = ['status', 'title', 'friendly_url', 'description', 'author_id', 'company_id', 'no_activity_notified_at'];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id', 'id');
    }

    public function headerImage(): MorphOne
    {
        return $this->morphOne(Media::class, 'model')
            ->where('collection_name', config('custom.media_collections.partner_page.header_image'));
    }

    public function sections(?string $layout = null): HasMany
    {
        return $this->hasMany(PartnerPageSection::class, 'partner_page_id', 'id')
            ->when($layout !== null, function ($query) use ($layout) {
                $query->where('layout', $layout);
            });
    }

    public function contents(): HasManyThrough
    {
        return $this->hasManyThrough(PartnerPageSectionContent::class, PartnerPageSection::class,
            'partner_page_id', 'partner_page_section_id', 'id', 'id');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(config('custom.media_collections.partner_page.header_image'))->singleFile();
    }
}
