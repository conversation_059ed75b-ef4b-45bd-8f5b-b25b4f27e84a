<?php

namespace App\Virtual\Models;

/**
 * @OA\Schema(
 *     title="State",
 *     description="State model",
 *
 *     @OA\Xml(
 *         name="State"
 *     )
 * )
 */
class State
{
    /**
     * @OA\Property(
     *     title="Id",
     *     description="Id",
     *     format="int64",
     *     example=643702075276764945
     * )
     *
     * @var int
     */
    private $id;

    /**
     * @OA\Property(
     *     title="country_id",
     *     description="country_id",
     *     format="int64",
     *     example=231
     * )
     *
     * @var int
     */
    private $country_id;

    /**
     * @OA\Property(
     *     title="name",
     *     description="name",
     *     example="Nevada"
     * )
     *
     * @var string
     */
    private $short_code;

    /**
     * @OA\Property(
     *     title="abbreviation",
     *     description="abbreviation",
     *     example="NV"
     * )
     *
     * @var string
     */
    private $abbreviation;
}
