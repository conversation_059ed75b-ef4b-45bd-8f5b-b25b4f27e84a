<?php

namespace App\Virtual\Models;

use phpseclib3\Math\BigInteger;

/**
 * @OA\Schema(
 *     title="FeatureFlag",
 *     description="Feature flag model",
 *
 *     @OA\Xml(
 *         name="FeatureFlag"
 *     )
 * )
 */
class FeatureFlag
{
    /**
     * @OA\Property(
     *     title="Id",
     *     description="Id",
     *     format="int64",
     *     example=643702075276764945
     * )
     */
    private BigInteger $id;

    /**
     * @OA\Property(
     *     title="name",
     *     description="name",
     *     example="THE_NAME"
     * )
     */
    private string $name;

    /**
     * @OA\Property(
     *     title="activated",
     *     description="activated",
     *     example="true"
     * )
     */
    private bool $activated;

    /**
     * @OA\Property(
     *     title="Created at",
     *     description="Created at",
     *     example="2020-01-27 17:50:45",
     *     format="datetime",
     *     type="string"
     * )
     */
    private \DateTime $created;

    /**
     * @OA\Property(
     *     title="Updated at",
     *     description="Updated at",
     *     example="2020-01-27 17:50:45",
     *     format="datetime",
     *     type="string"
     * )
     */
    private \DateTime $updated;
}
