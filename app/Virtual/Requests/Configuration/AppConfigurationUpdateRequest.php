<?php

namespace App\Virtual\Requests\Configuration;

/**
 * @OA\Schema(
 *     title="Store App configurations request",
 *     description="Store App configurations request body data",
 *     type="object",
 *     required={"key", "value"}
 * )
 */
class AppConfigurationUpdateRequest
{
    /**
     * @OA\Property(
     *     title="key",
     *     description="The key",
     *     example="SHOUT_OUT_CLICKABLE"
     * )
     */
    private string $key;

    /**
     * @OA\Property(
     *     title="value",
     *     description="The value",
     *     example="false"
     * )
     */
    private string $value;
}
