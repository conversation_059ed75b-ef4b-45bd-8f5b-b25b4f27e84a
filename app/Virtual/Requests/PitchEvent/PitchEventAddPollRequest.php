<?php

namespace App\Virtual\Requests\PitchEvent;

use phpseclib3\Math\BigInteger;

/**
 * @OA\Schema(
 *     title="Add Poll to a Pitch Event",
 *     description="Adds a Poll to the pitch event",
 *     type="object",
 *     required={"pitch_event_id, poll_question_id"}
 * )
 */
class PitchEventAddPollRequest
{
    /**
     * @OA\Property(
     *     title="pitch_event_id",
     *     description="The pitch event ID",
     *     example="101"
     * )
     */
    public BigInteger $pitch_event_id;

    /**
     * @OA\Property(
     *     title="poll_question_id",
     *     description="The Poll Question",
     *     example="201"
     * )
     */
    public BigInteger $poll_question_id;
}
