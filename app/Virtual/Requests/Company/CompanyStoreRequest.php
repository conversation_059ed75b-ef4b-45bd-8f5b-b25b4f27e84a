<?php

namespace App\Virtual\Requests\Company;

use phpseclib3\Math\BigInteger;

/**
 * @OA\Schema(
 *     title="Store Company request",
 *     description="Store Company request body data",
 *     type="object",
 *     required={"name", "company_type"}
 * )
 */
class CompanyStoreRequest
{
    /**
     * @OA\Property(
     *     title="name",
     *     description="name",
     *     example="Company 1"
     * )
     */
    private string $name;

    /**
     * @OA\Property(
     *     title="description",
     *     description="description",
     *     example="Company description 1"
     * )
     */
    private string $description;

    /**
     * @OA\Property(
     *     title="company_type",
     *     description="company_type",
     *     example="entity"
     * )
     */
    private string $company_type;

    /**
     * @OA\Property(
     *     title="revenue",
     *     description="revenue",
     *     example="active"
     * )
     */
    private int $revenue;

    /**
     * @OA\Property(
     *     title="address",
     *     description="address",
     *     example="the address"
     * )
     */
    private string $address;

    /**
     * @OA\Property(
     *     title="address2",
     *     description="address2",
     *     example="the address2"
     * )
     */
    private string $address2;

    /**
     * @OA\Property(
     *     title="city",
     *     description="city",
     *     example="Bogotá"
     * )
     */
    private string $city;

    /**
     * @OA\Property(
     *     title="StateId",
     *     description="State Id",
     *     format="int64",
     *     example=643702075276764945
     * )
     */
    private BigInteger $stateId;

    /**
     * @OA\Property(
     *     title="zip",
     *     description="zip",
     *     example="Bogotá"
     * )
     */
    private string $zip;

    /**
     * @OA\Property(
     *     title="CountryId",
     *     description="Country Id",
     *     format="int64",
     *     example=643702075276764945
     * )
     */
    private BigInteger $countryId;

    /**
     * @OA\Property(
     *     title="Phone",
     *     description="Phone",
     *     example="555 548471"
     * )
     */
    private string $phone;

    /**
     * @OA\Property(
     *     title="Industry",
     *     description="Industry",
     *     example="The industry"
     * )
     */
    private string $industry;

    /**
     * @OA\Property(
     *     title="EmployeeRange",
     *     description="Employee Range",
     *     example="0-10"
     * )
     */
    private string $employeeRange;

    /**
     * @OA\Property(
     *     title="Founded",
     *     description="Founded year",
     *     example="2021"
     * )
     */
    private int $founded;

    /**
     * @OA\Property(
     *     title="profile_vendor_handle",
     *     description="Company handle",
     *     example="companyhandle"
     * )
     */
    private string $profileVendorHandle;

    /**
     * @OA\Property(
     *     title="profile_company_website_url",
     *     description="Company website URL",
     *     example="www.testing.com"
     * )
     */
    private string $profileCompanyWebsiteUrl;

    /**
     * @OA\Property(
     *     title="profile_company_friendly_name",
     *     description="A friendly company name",
     *     example="My friendly cmpany name"
     * )
     */
    private string $profileCompanyFriendlyName;
}
