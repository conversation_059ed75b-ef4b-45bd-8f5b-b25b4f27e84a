<?php

namespace App\Virtual\Requests\Media;

/**
 * @OA\Schema(
 *     title="Update document properties",
 *     description="Update document",
 *     type="object"
 * )
 */
class UpdateMediaDocumentPropertiesRequest
{
    /**
     * @OA\Property(
     *      title="categories",
     *      description="The video categories",
     *      type="array",
     *
     *      @OA\Items(
     *          type="string"
     *      ),
     *      example="[123,456,789]"
     * )
     */
    public array $categories;

    /**
     * @OA\Property(
     *     title="title",
     *     description="The title to show on the video",
     *     type="string",
     *     example="The video title"
     * )
     */
    public string $title;

    /**
     * @OA\Property(
     *     title="description",
     *     description="A short description about the media content",
     *     type="string",
     *     example="Everything about the contents of the media"
     * )
     */
    public string $description;

    /**
     * @OA\Property(
     *     title="Replace Categories",
     *     description="Controls if media categories will be appended or replaced",
     *     type="boolean",
     *     example="1"
     * )
     */
    public bool $replaceCategories;
}
