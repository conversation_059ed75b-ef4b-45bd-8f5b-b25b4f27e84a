<?php

namespace App\Virtual\Requests\VendorSiteSeo;

use phpseclib3\Math\BigInteger;

/**
 * @OA\Schema(
 *     title="VendorSiteSeoUpdateRequest",
 *     description="Handle an incoming vendor site seo update request",
 *     type="object",
 *     required={"id"}
 * )
 */
class VendorSiteSeoUpdateRequest
{
    /**
     * @OA\Property(
     *     title="id",
     *     description="The ID",
     *     example="6111544121548"
     * )
     */
    public BigInteger $id;

    /**
     * @OA\Property(
     *     title="title",
     *     description="This is a 70 character long title with a lot of padding to make it so!",
     *     example="One of the best titles ever"
     * )
     */
    public string $title;

    /**
     * @OA\Property(
     *     title="description",
     *     description="This is a 200 character long description of this web page which is quite interesting and which describes its contents well with a lot of relevant keywords and isn\'t just general marketing mumbo-jumbo.",
     *     example="An option for the question"
     * )
     */
    public string $description;

    /**
     * @OA\Property(
     *      title="keywords",
     *
     *      @OA\Items(
     *          type="array",
     *          @OA\Items()
     *      ),
     *      description="The keywords",
     *      example="['Keyword1', 'Keyword2']"
     * )
     */
    public array $keywords;
}
