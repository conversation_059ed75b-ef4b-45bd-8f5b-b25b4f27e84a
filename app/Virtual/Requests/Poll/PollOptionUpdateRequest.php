<?php

namespace App\Virtual\Requests\Poll;

use phpseclib3\Math\BigInteger;

/**
 * @OA\Schema(
 *     title="Poll option update",
 *     description="Handle an incoming poll option update request",
 *     type="object",
 *     required={"id"}
 * )
 */
class PollOptionUpdateRequest
{
    /**
     * @OA\Property(
     *     title="id",
     *     description="The poll question ID",
     *     example="656545153"
     * )
     */
    public BigInteger $id;

    /**
     * @OA\Property(
     *     title="option",
     *     description="The poll option",
     *     example="Yes I like it"
     * )
     */
    public string $option;

    /**
     * @OA\Property(
     *     title="Shows on reports",
     *     description="Controls if this option should be shown on reports",
     *     example=true
     * )
     */
    public bool $shows_on_reports;
}
