<?php

namespace App\Virtual\Requests\Poll;

use phpseclib3\Math\BigInteger;

/**
 * @OA\Schema(
 *     title="Poll question update",
 *     description="Handle an incoming poll question update request",
 *     type="object",
 *     required={"question"}
 * )
 */
class PollQuestionUpdateRequest
{
    /**
     * @OA\Property(
     *     title="id",
     *     description="The poll question ID",
     *     example="656545153"
     * )
     */
    public BigInteger $id;

    /**
     * @OA\Property(
     *     title="question",
     *     description="The poll question",
     *     example="Any questions?"
     * )
     */
    public string $question;

    /**
     * @OA\Property(
     *     title="Question type",
     *     description="Type of the question (QuestionType Enum)",
     *     example="MultipleChoice"
     * )
     */
    public string $question_type;

    /**
     * @OA\Property(
     *     title="Poll category",
     *     description="The category of the question poll (PollCategory Enum)",
     *     example="AwardPoll"
     * )
     */
    public string $poll_category;

    /**
     * @OA\Property(
     *     title="Show aditional feedback",
     *     description="Controls if the question shoud show a field for aditional feedback",
     *     example=true
     * )
     */
    public bool $show_additional_feedback;
}
