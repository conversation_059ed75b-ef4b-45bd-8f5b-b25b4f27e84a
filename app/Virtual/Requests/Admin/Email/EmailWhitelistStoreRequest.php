<?php

namespace App\Virtual\Requests\Admin\Email;

use phpseclib3\Math\BigInteger;

/**
 * @OA\Schema(
 *     title="Store Email Whitelist request",
 *     description="Store Email Whitelist request body data",
 *     type="object",
 *     required={"id"}
 * )
 */
class EmailWhitelistStoreRequest
{
    /**
     * @OA\Property(
     *     title="domain",
     *     description="domain",
     *     example="channelprogram.com"
     * )
     */
    private string $domain;

    /**
     * @OA\Property(
     *     title="user_id_responsible",
     *     description="The user_id_responsible",
     *     example="51516212154"
     * )
     */
    private BigInteger $user_id_responsible;

    /**
     * @OA\Property(
     *     title="reason",
     *     description="reason",
     *     example="Not a bad domain"
     * )
     */
    private string $reason;
}
