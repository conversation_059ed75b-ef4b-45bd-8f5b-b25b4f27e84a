<?php

namespace App\Virtual\Requests\Admin\ProfanityFilter;

use phpseclib3\Math\BigInteger;

/**
 * @OA\Schema(
 *     title="Delete Profanity Filter request",
 *     description="Delete Profanity Filter request body data",
 *     type="object",
 *     required={"id"}
 * )
 */
class ProfanityFilterUpdateRequest
{
    /**
     * @OA\Property(
     *     title="id",
     *     description="The id",
     *     example="51516212154"
     * )
     */
    private BigInteger $id;

    /**
     * @OA\Property(
     *     title="bad_word",
     *     description="$bad_word",
     *     example="CEO"
     * )
     */
    private string $bad_word;
}
