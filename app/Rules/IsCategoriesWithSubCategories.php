<?php

namespace App\Rules;

use App\Models\Category\Category;
use App\Models\Company\Company;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Gate;

class IsCategoriesWithSubCategories implements ValidationRule
{
    private string $message = 'Must select a minimum of 1 subcategory per selected category.';

    private Company $company;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($company)
    {
        $this->company = $company;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $subCategories = Category::whereIn('id', $value)->get();
        $parentCategoryIds = $subCategories->whereNull('parent_id')->pluck('id')->unique();
        if (count($parentCategoryIds) > 0) {
            $response = Gate::inspect(
                'add-company-product-category',
                [$this->company, $parentCategoryIds]
            );
            if (!$response->allowed()) {
                $this->message = $response->message();
                $fail($this->message);

                return;
            }
            foreach ($parentCategoryIds as $categoryId) {
                $subCategoryIds = $subCategories->where('parent_id', $categoryId)->pluck('id');
                if (count($subCategoryIds) === 0) {
                    $fail($this->message);

                    return;
                }
                $response = Gate::inspect(
                    'add-company-product-sub-category',
                    [$this->company, $subCategoryIds]
                );
                if (!$response->allowed()) {
                    $this->message = $response->message();
                    $fail($this->message);
                }
            }
        }
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return $this->message;
    }
}
