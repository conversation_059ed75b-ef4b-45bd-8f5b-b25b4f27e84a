<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class FileSizeByType implements ValidationRule
{
    private $allowedMimeTypesAndSizes;
    private $defaultMaxSize;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(array $allowedMimeTypesAndSizes, ?int $defaultMaxSize = 5)
    {
        $this->defaultMaxSize = $defaultMaxSize;
        $this->allowedMimeTypesAndSizes = $allowedMimeTypesAndSizes;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $maxSize = $this->defaultMaxSize;
        $ext = $value->getClientOriginalExtension();
        foreach ($this->allowedMimeTypesAndSizes as $rule) {
            if (!isset($rule['types']) || !is_array($rule['types']) || !isset($rule['size'])) {
                continue;
            }

            if (in_array($ext, $rule['types'])) {
                $maxSize = $rule['size'];

                break;
            }
        }
        $maxSize *= 1024 * 1024;
        if ($value->getSize() > $maxSize) {
            $fail("The {$attribute} must not exceed " . ($maxSize / (1024 * 1024)) . "MB.");
        }
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return 'The field :attribute is too big';
    }
}
