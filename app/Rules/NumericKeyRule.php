<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class NumericKeyRule implements ValidationRule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(private ?string $customMessage = null) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        foreach ($value as $key => $item) {
            if (!is_numeric($key)) {
                $fail($this->message($attribute));
            }
        }
    }

    public function message(string $attribute)
    {
        return $this->customMessage ?? 'The keys for object "' . $attribute . '" must be numeric.';
    }
}
