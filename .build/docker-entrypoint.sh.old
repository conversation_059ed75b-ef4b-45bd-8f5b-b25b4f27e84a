#!/bin/bash
chown nginx:nobody -R /var/www/html/storage/
chmod 775 -R /var/www/html/
chmod 777 -R  /var/www/html/storage/
rm -rf /etc/nginx/conf.d/* 
export $(egrep -v '^#' /.env | xargs)
/usr/sbin/nginx -c /etc/nginx/nginx.conf 
php-fpm7 -F &
crond &
php artisan migrate --force 
php artisan optimize &
sleep 2
touch /var/www/html/storage/logs/laravel.log
tail -f /var/www/html/storage/logs/laravel.log&
sleep infinity
